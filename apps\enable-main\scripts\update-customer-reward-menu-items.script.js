//script to replace menu item objects in customer rewards
// This script updates customers' rewards with new menu item data for specific masterMenuItemIds

print('Starting customer reward menu items update...');

// Define the menu item replacements
const menuItemReplacements = {
  '680f5be47ac14a0aaabebd4b': {
    brandId: ObjectId('67b5b5802381638cc41ef125'),
    menuId: ObjectId('67b5dae7e47943099e2865d7'),
    masterMenuItemId: ObjectId('680f5be47ac14a0aaabebd4b'),
    nameEn: 'Free Beverage',
    images: [],
    eligibleMenuItemIds: [ObjectId('680f5be47ac14a0aaabebd4b')],
  },
  '680f5c28fb1d3ea31ea09410': {
    brandId: ObjectId('67b5b5802381638cc41ef125'),
    menuId: ObjectId('6858204e953f5eb1dcd66931'),
    masterMenuItemId: ObjectId('68582055953f5eb1dcd669c6'),
    nameEn: 'Free Breakfast',
    images: [],
    eligibleMenuItemIds: [ObjectId('68582055953f5eb1dcd669c6')],
  },
};

const masterMenuItemIds = Object.keys(menuItemReplacements).map((id) =>
  ObjectId(id),
);

print(
  'Menu item IDs to update:',
  masterMenuItemIds.map((id) => id.toString()),
);

// Count customers that will be affected
const customerCount = db.customers.countDocuments({
  'rewards.source': 'punch_card_progress',
  'rewards.benefit': 'menu_item',
  'rewards.menuItem.masterMenuItemId': { $in: masterMenuItemIds },
  deletedAt: null,
});

print(`Found ${customerCount} customers with matching rewards`);

// Prepare bulk update operations using array filters
const bulkOps = [];

Object.keys(menuItemReplacements).forEach((masterMenuItemIdStr) => {
  const masterMenuItemId = ObjectId(masterMenuItemIdStr);
  const newMenuItem = menuItemReplacements[masterMenuItemIdStr];

  print(
    `Preparing update for menu item: ${newMenuItem.nameEn} (${masterMenuItemId})`,
  );

  // Create bulk update operation for rewards array
  bulkOps.push({
    updateMany: {
      filter: {
        'rewards.source': 'punch_card_progress',
        'rewards.benefit': 'menu_item',
        'rewards.menuItem.masterMenuItemId': masterMenuItemId,
        deletedAt: null,
      },
      update: {
        $set: {
          'rewards.$[elem].menuItem': newMenuItem,
        },
      },
      arrayFilters: [
        {
          'elem.source': 'punch_card_progress',
          'elem.benefit': 'menu_item',
          'elem.menuItem.masterMenuItemId': masterMenuItemId,
        },
      ],
    },
  });
});

// Execute bulk update operations
let totalModified = 0;
let totalMatched = 0;

if (bulkOps.length > 0) {
  const bulkResult = db.customers.bulkWrite(bulkOps);
  totalModified = bulkResult.modifiedCount;
  totalMatched = bulkResult.matchedCount;
} else {
  print('No bulk operations to execute');
}

// Summary
print('UPDATE SUMMARY');
print('='.repeat(50));
print(`Menu items to update: ${Object.keys(menuItemReplacements).length}`);
print(`Customers found: ${customerCount}`);
print(`Documents matched: ${totalMatched}`);
print(`Documents modified: ${totalModified}`);
print('Customer reward menu items update completed!');
