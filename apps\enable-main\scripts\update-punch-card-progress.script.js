// MongoDB shell script to replace punch card objects in customer progress
// This script updates customers' punch card progress with new punch card data from the database

print("Starting punch card progress update...");

// Define the punch card IDs to update
const punchCardIdsToUpdate = [
  ObjectId("680f5cda1484c5184689ae86"),
  ObjectId("680f5cf91484c5184689af3a")
];

print("Punch card IDs to update:", punchCardIdsToUpdate.map(id => id.toString()));

// Fetch the updated punch card objects from the database
const updatedPunchCards = {};

punchCardIdsToUpdate.forEach(punchCardId => {
  const punchCard = db.punchcards.findOne({ _id: punchCardId });
  if (punchCard) {
    // Create embedded punch card object with only the required fields
    updatedPunchCards[punchCardId.toString()] = {
      _id: punchCard._id,
      nameEn: punchCard.nameEn,
      nameAr: punchCard.nameAr,
      achievements: punchCard.achievements,
      counter: punchCard.counter,
      hasStampOnEarn: punchCard.hasStampOnEarn,
      hasStampOnRedeem: punchCard.hasStampOnRedeem,
      track: punchCard.track,
      emptyStamp: punchCard.emptyStamp,
      filledStamp: punchCard.filledStamp
    };
    print(`Found punch card: ${punchCard.nameEn} (${punchCardId})`);
  } else {
    print(`WARNING: Punch card not found: ${punchCardId}`);
  }
});

// Count customers that will be affected
const customerCount = db.customers.countDocuments({
  "punchCardProgress.punchCard._id": { $in: punchCardIdsToUpdate },
  deletedAt: null
});

// Prepare bulk update operations using array filters
const bulkOps = [];

punchCardIdsToUpdate.forEach(punchCardId => {
  const punchCardIdStr = punchCardId.toString();
  const updatedPunchCard = updatedPunchCards[punchCardIdStr];

  if (updatedPunchCard) {
    // Create bulk update operation with array filter
    bulkOps.push({
      updateMany: {
        filter: {
          "punchCardProgress.punchCard._id": punchCardId,
          deletedAt: null
        },
        update: {
          $set: {
            "punchCardProgress.$[elem].punchCard": updatedPunchCard
          }
        },
        arrayFilters: [
          { "elem.punchCard._id": punchCardId }
        ]
      }
    });
  }
});

// Execute bulk update operations
let totalModified = 0;
let totalMatched = 0;

if (bulkOps.length > 0) {
  const bulkResult = db.customers.bulkWrite(bulkOps);
  totalModified = bulkResult.modifiedCount;
  totalMatched = bulkResult.matchedCount;
} else {
  print("No bulk operations to execute");
}

// Summary
print("UPDATE SUMMARY");
print("=".repeat(50));
print(`Punch cards to update: ${punchCardIdsToUpdate.length}`);
print(`Customers found: ${customerCount}`);
print(`Documents matched: ${totalMatched}`);
print(`Documents modified: ${totalModified}`);
print("Punch card progress update completed!");
