import { CustomerDocument, RegisteredPass } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { CustomerPunchCardServiceInterface } from '../customer-punch-card/customer-punch-card.service.interface';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerPassServiceInterface } from './customer-pass.service.interface';

@Injectable()
export class CustomerPassService implements CustomerPassServiceInterface {
  constructor(
    @Inject(CustomerPunchCardServiceInterface)
    private readonly customerPunchCardService: CustomerPunchCardServiceInterface,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
  ) {}

  public async applyPassGenerationPreFunctions(
    customer: CustomerDocument,
  ): Promise<void> {
    await this.customerPunchCardService.initPunchCardProgress(customer);
  }

  async applyPassUpdatePostFunctions(
    registeredPass: RegisteredPass,
  ): Promise<void> {
    await this.customerRepository.updateRegisteredPassTimestamp(registeredPass);
  }

  public async savePassLink(
    customer: CustomerDocument,
    brandId: string,
    passLink: string,
  ): Promise<void> {
    await this.customerRepository.updateOne(
      { _id: customer._id },
      { $set: { [`googlePassLinks.${brandId}`]: passLink } },
    );
  }
}
