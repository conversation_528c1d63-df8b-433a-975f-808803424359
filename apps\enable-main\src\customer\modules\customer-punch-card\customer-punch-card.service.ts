import {
  CustomerDocument,
  EarnedReward,
  EmbeddedPunch<PERSON>ardDto,
  mapAsync,
  MAX_PUNCH_CARDS,
  NextAchievement,
  OrderToComplete,
  PunchCardCounter,
  PunchCardDocument,
  PunchCardProgress,
  RequirementType,
  RewardSource,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { CompletedPunchCardService } from '../../../punch-card/modules/completed-punch-card/completed-punch-card.service';
import { PunchCardReadService } from '../../../punch-card/modules/punch-card-read/punch-card-read.service';
import { CustomerReadServiceInterface } from '../customer-read/customer-read.service.interface';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerPunchCardServiceInterface } from './customer-punch-card.service.interface';

@Injectable()
export class CustomerPunchCardService
  implements CustomerPunchCardServiceInterface
{
  constructor(
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    private readonly punchCardReadService: PunchCardReadService,
    private readonly completedPunchCardService: CompletedPunchCardService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async incrementPunchCardCounters(
    customer: CustomerDocument,
    order?: OrderToComplete,
  ) {
    await this.initPunchCardProgress(customer);
    if (!customer.punchCardProgress) return;
    if (customer.punchCardProgress.length === 0) return;

    const progressToIncrement = customer.punchCardProgress.filter(
      (progress) => !progress.completedAt,
    );

    const incrementedProgresses = await mapAsync(
      progressToIncrement,
      (progress) => this.incrementProgress(customer, progress, order),
    );
    const unaffectedProgress = customer.punchCardProgress.filter(
      (progress) => !progressToIncrement.includes(progress),
    );
    // Update the current customer instance
    customer.punchCardProgress = [
      ...unaffectedProgress,
      ...incrementedProgresses,
    ];
    //update the current customer in the database
    await this.customerRepository.updateOne(
      { _id: customer._id },
      {
        $set: {
          punchCardProgress: customer.punchCardProgress,
        },
      },
    );

    this.eventEmitter.emit('customer.stamps.updated', customer);
  }

  private async setProgressCount(
    customer: CustomerDocument,
    progress: PunchCardProgress,
    newCount: number,
  ): Promise<PunchCardProgress> {
    if (progress.count === newCount) return progress;
    const incrementedProgress = {
      ...progress,
      count: newCount,
      incrementedAt: moment.utc().toDate(),
    };
    await this.checkForEarnedAchievements(customer, [incrementedProgress]);
    return await this.completePunchCardProgress(customer, incrementedProgress);
  }

  private async incrementProgress(
    customer: CustomerDocument,
    progress: PunchCardProgress,
    order: OrderToComplete,
  ): Promise<PunchCardProgress> {
    const incrementAmount = this.getIncrementAmount(progress, order);
    const newCount = progress.count + incrementAmount;
    const completionThreshold =
      this.punchCardReadService.getCompletionThreshold(progress.punchCard);

    // Happy scenario - no rollover
    if (newCount < completionThreshold) {
      return await this.setProgressCount(customer, progress, newCount);
    }

    const amountToIncrement = completionThreshold - progress.count;
    const amountToRollover = incrementAmount - amountToIncrement;
    const incrementedProgress = await this.setProgressCount(
      customer,
      progress,
      progress.count + amountToIncrement,
    );

    const newCompletionThreshold =
      this.punchCardReadService.getCompletionThreshold(
        incrementedProgress.punchCard,
      );
    const rolloversNeeded = Math.floor(
      amountToRollover / newCompletionThreshold,
    );
    const completedProgresses = this.createCompletedProgress(
      incrementedProgress.punchCard,
      rolloversNeeded,
    );
    await this.completedPunchCardService.bulkSave(
      customer,
      completedProgresses,
    );

    customer = await this.checkForEarnedAchievements(
      customer,
      completedProgresses,
    );

    const newProgressCount = amountToRollover % newCompletionThreshold;
    const newProgress = await this.setProgressCount(
      customer,
      incrementedProgress,
      newProgressCount,
    );

    return newProgress;
  }

  private createCompletedProgress(
    punchCard: EmbeddedPunchCardDto,
    count: number,
  ) {
    return Array.from({ length: count })
      .map(() => new PunchCardProgress(punchCard))
      .map((progress) => ({
        ...progress,
        count: this.punchCardReadService.getCompletionThreshold(
          progress.punchCard,
        ),
        incrementedAt: moment.utc().toDate(),
        completedAt: moment.utc().toDate(),
      }));
  }

  public getIncrementAmount(
    progress: PunchCardProgress,
    order: OrderToComplete,
  ): number {
    const counter = progress.punchCard.counter;
    if (counter.type === RequirementType.NUMBER_OF_ORDERS) return 1;

    const completionThreshold =
      this.punchCardReadService.getCompletionThreshold(progress.punchCard);
    const isRedemptionStamp =
      progress.punchCard.hasStampOnRedeem &&
      progress.count === completionThreshold - 1;
    const minIncrement = isRedemptionStamp ? 1 : 0;

    if (counter.type === RequirementType.NUMBER_OF_LOYALTY_ORDERS)
      return order.isLoyaltyOrder ? 1 : minIncrement;

    const hasOrderItems = order && order.items && order.items.length > 0;
    if (!hasOrderItems) return minIncrement;

    const menuItemIds = counter.menuItem.eligibleMenuItemIds.map((id) =>
      id.toString(),
    );

    const orderItems = order.items
      .filter((item) => item.itemReference)
      .filter((item) => menuItemIds.includes(item.itemReference.toString()))
      .map((item) => item.quantity)
      .reduce((a, b) => a + b, 0);

    return Math.max(orderItems, minIncrement);
  }

  async findPunchCardsToInit(
    customer: CustomerDocument,
  ): Promise<PunchCardDocument[]> {
    const punchCards = await this.punchCardReadService.index({
      companyId: customer.company,
    });
    if (!punchCards) return;

    const customerPunchCardIds = (customer.punchCardProgress || []).map(
      (progress) => progress.punchCard._id.toString(),
    );
    const eligiblePunchCards =
      customerPunchCardIds.length > 0
        ? punchCards.filter(
            ({ _id }) => !customerPunchCardIds.includes(_id.toString()),
          )
        : punchCards;
    const punchCardsToInit = eligiblePunchCards.filter(
      (punchCard) => punchCard.achievements?.length > 0,
    );
    return punchCardsToInit;
  }

  async initPunchCardProgress(customer: CustomerDocument) {
    const punchCardsToInit = await this.findPunchCardsToInit(customer);
    if (punchCardsToInit.length === 0) return;

    const newProgresses = punchCardsToInit.map(
      (punchCard) => new PunchCardProgress(punchCard),
    );
    customer.punchCardProgress = [
      ...(customer.punchCardProgress || []),
      ...newProgresses,
    ];
    await customer.updateOne({ punchCardProgress: customer.punchCardProgress });
  }

  private getPastAchievementIds(
    customer: CustomerDocument,
    progress?: PunchCardProgress,
  ): string[] {
    return (customer.rewards || [])
      .concat(customer.usedRewards || [])
      .filter((reward) => !progress || progress._id.equals(reward.progressId))
      .map((reward) => reward.achievementId.toHexString());
  }

  private async checkForEarnedAchievements(
    customer: CustomerDocument,
    progresses: PunchCardProgress[],
  ): Promise<CustomerDocument> {
    const rewardsToEarn = progresses.flatMap((progress) =>
      this.getProgressEarnedRewards(customer, progress),
    );

    if (rewardsToEarn.length === 0) return customer;

    const updatedCustomer = await this.customerRepository.addRewardsToCustomer(
      customer,
      rewardsToEarn,
    );
    this.eventEmitter.emit('customer.rewards.updated', updatedCustomer);
    return updatedCustomer;
  }

  public getProgressEarnedRewards(
    customer: CustomerDocument,
    progress: PunchCardProgress,
  ): EarnedReward[] {
    const pastAchievementIds = this.getPastAchievementIds(customer, progress);

    return progress.punchCard.achievements
      .filter(({ requirement }) => requirement?.targetValue <= progress.count)
      .filter(({ _id }) => !pastAchievementIds.includes(_id.toString()))
      .map((achievement) => ({
        ...achievement.reward,
        _id: new Types.ObjectId(),
        achievementId: achievement._id,
        progressId: progress._id,
        earnedAt: moment.utc().toDate(),
        source: RewardSource.PUNCH_CARD_PROGRESS,
      }));
  }

  private async completePunchCardProgress(
    customer: CustomerDocument,
    progress: PunchCardProgress,
  ): Promise<PunchCardProgress> {
    const completionThreshold =
      this.punchCardReadService.getCompletionThreshold(progress.punchCard);
    if (progress.count < completionThreshold) return progress;

    const punchCard = await this.punchCardReadService.findById(
      progress.punchCard._id,
    );
    const newProgress = new PunchCardProgress(punchCard);
    await this.completedPunchCardService.save(customer, progress);
    return newProgress;
  }

  async getNextAchievement(
    customer: CustomerDocument,
  ): Promise<NextAchievement> {
    const nextAchievements = await this.getNextAchievements(customer);
    return nextAchievements
      .filter((achievement) => achievement)
      .reduce(
        (previous, current) =>
          previous && previous.remainingAmount < current.remainingAmount
            ? previous
            : current,
        null,
      );
  }

  async getNextAchievements(
    customer: CustomerDocument,
  ): Promise<NextAchievement[]> {
    await this.initPunchCardProgress(customer);

    const activeProgresses = (customer.punchCardProgress || []).filter(
      (progress) => !progress.completedAt,
    );

    return Array.from({ length: MAX_PUNCH_CARDS }, (_, index) =>
      activeProgresses.find((progress) => progress.punchCard.track === index),
    ).map((progress) => {
      if (!progress) return null;

      const nextAchievement = progress.punchCard.achievements.find(
        (achievement) => achievement?.requirement?.targetValue > progress.count,
      );
      if (!nextAchievement) return null;

      const remainingOrders =
        nextAchievement?.requirement?.targetValue - progress.count;
      return {
        ...nextAchievement,
        punchCardNameEn: progress.punchCard.nameEn,
        punchCardNameAr: progress.punchCard.nameAr,
        remainingAmount: remainingOrders,
      };
    });
  }

  async getRemainingStampsNextReward(
    customer: CustomerDocument,
  ): Promise<number | null> {
    const nextAchievementPerTrack = await this.getNextAchievements(customer);
    const nextAchievements = nextAchievementPerTrack.filter(
      (achievement) => achievement,
    );
    if (!nextAchievements || nextAchievements.length === 0) return null;
    return Math.min(
      ...nextAchievements.map((achievement) => achievement.remainingAmount),
    );
  }

  async getRewardsUnlockedOnIncrement(
    customer: CustomerDocument,
    progress: PunchCardProgress,
    increment: number,
  ): Promise<number> {
    const completionThreshold =
      this.punchCardReadService.getCompletionThreshold(progress.punchCard);

    const newCount = progress.count + increment;
    const earnedRewards = this.getProgressEarnedRewards(customer, {
      ...progress,
      count: newCount,
    });
    if (newCount < completionThreshold) return earnedRewards.length;

    const amountToIncrement = completionThreshold - progress.count;
    const amountToRollover = increment - amountToIncrement;

    const punchCard = await this.punchCardReadService.findById(
      progress.punchCard._id,
    );

    const newCompletionThreshold =
      this.punchCardReadService.getCompletionThreshold(punchCard);
    const rolloversNeeded = Math.floor(
      amountToRollover / newCompletionThreshold,
    );
    const rewardsPerCompletion = punchCard.achievements.length;
    const rewardsFromCompletions = rewardsPerCompletion * rolloversNeeded;

    const newProgressCount = amountToRollover % newCompletionThreshold;
    const newProgress = new PunchCardProgress(punchCard);
    const newProgressEarnedRewards = this.getProgressEarnedRewards(customer, {
      ...newProgress,
      count: newProgressCount,
    });

    return (
      earnedRewards.length +
      rewardsFromCompletions +
      newProgressEarnedRewards.length
    );
  }

  async getPunchCardCounters(
    companyId: Types.ObjectId,
    customerId?: Types.ObjectId,
  ): Promise<PunchCardCounter[]> {
    const punchCards = await this.punchCardReadService.index({
      companyId,
      counterType: RequirementType.NUMBER_OF_MENU_ITEMS,
    });
    if (!customerId) return punchCards.map((punchCard) => punchCard.counter);

    const customer = await this.customerReadService.findOne(
      customerId.toString(),
    );

    if (!customer.company.equals(companyId))
      throw new BadRequestException(
        `Customer does not belong to the given company`,
      );

    if (!customer.punchCardProgress || customer.punchCardProgress.length === 0)
      return punchCards.map((punchCard) => punchCard.counter);

    const activePunchCards = customer.punchCardProgress
      .filter((progress) => !progress.completedAt)
      .map((progress) => progress.punchCard);

    const activePunchCardIds = activePunchCards.map((punchCard) =>
      punchCard._id.toString(),
    );

    const inactivePunchCards = punchCards.filter(
      (punchCard) => !activePunchCardIds.includes(punchCard._id.toString()),
    );

    return activePunchCards
      .filter(
        (punchCard) =>
          punchCard.counter.type === RequirementType.NUMBER_OF_MENU_ITEMS,
      )
      .concat(inactivePunchCards)
      .map((punchCard) => punchCard.counter);
  }
}
