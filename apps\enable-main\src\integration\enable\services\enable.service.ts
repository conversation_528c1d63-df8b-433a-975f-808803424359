import {
  AggregatorOrderToCreate,
  BenefitType,
  CacheKeys,
  CapturedOrderSource,
  CompanyDocument,
  CouponDocument,
  CreateShipmentDto,
  CurrentUser,
  CustomerDocument,
  deprecatedPaymentMethodMapping,
  EarnedReward,
  EmbeddedTierDto,
  fullCacheKeys,
  GetIntegrationLoyaltyConfigResponseDto,
  HandoffSchemeToDeliveryActionMap,
  HelperSharedServiceInterface,
  IHelperSharedService,
  IntegrationOrderCaptureDto,
  IntegrationUpdateTempCustomerDto,
  Language,
  LoyaltyStatus,
  LoyaltyTier,
  LRPSource,
  MenuItemToIndex,
  MenuToIndex,
  Order,
  OrderCreationSource,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderPaymentMethod,
  OrderPosToCreate,
  OrderSource,
  OrderStatusEnum,
  PostMicrosOrderDto,
  PunchCardBenefit,
  RegisterLoyaltyCustomerIntegrationDto,
  RegisterLoyaltyCustomerIntegrationResponseDto,
  responseCode,
  SavedLocationAddressType,
  SavedLocationToCreate,
  SavedLocationType,
  ShipmentStatus,
  ShopifyOrder,
  ShopifyProduct,
  ShopifySourceName,
  TempCustomerDocument,
  UnreachableError,
  WalletApp,
} from '@app/shared-stuff';
import { CacheServiceInterface } from '@app/shared-stuff/modules/cache/interfaces/cache.interface';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { ShipmentServiceInterface } from '../../../3pl/services/shipment/shipment-service.interface';
import { TempCustomerServiceInterface } from '../../../3pl/services/temp-customer/temp-customer-service.interface';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandService } from '../../../brand/services/brand/brand.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CouponServiceInterface } from '../../../coupon/services/coupon.service.interface';
import { CustomerLoyaltyMemberServiceInterface } from '../../../customer/modules/customer-loyalty-member/customer-loyalty-member.service.interface';
import { CustomerPassLinkServiceInterface } from '../../../customer/modules/customer-pass-link/customer-pass-link-service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerWebstoreServiceInterface } from '../../../customer/modules/customer-webstore/customer-webstore.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { AggregatorOrderService } from '../../../order/services/aggregatorOrder/aggregatorOrder.service';
import { OrderLogServiceInterface } from '../../../order/services/interfaces/order-log.service.interface';
import { OrderInvoiceService } from '../../../order/modules/order-invoice/order-invoice.service';
import { OrderPosService } from '../../../order/services/order-pos/order-pos.service';
import { OrderService } from '../../../order/services/order/order.service';
import { PaymentToCreate } from '../../../payment/dto/payment-to-create.dto';
import { PaymentService } from '../../../payment/services/payment/payment.service';
import { MenuCategoryService } from '../../../restaurant/services/menu-category/menu-category.service';
import { MenuItemService } from '../../../restaurant/services/menu-item/menu-item.service';
import { MenuService } from '../../../restaurant/services/menu/menu.service';
import { GetCustomerIntegrationDetailsDto } from '../../../shared/dto/get-customer-integration-details.dto';
import { IntegrationCoupon } from '../../../shared/types/integration-coupon.type';
import { IntegrationCustomerDetails } from '../../../shared/types/integration-customer-details.type';
import { IntegrationLoyaltyTier } from '../../../shared/types/integration-loyalty-tier.type';
import { IntegrationReward } from '../../../shared/types/integration-reward.type';
import { MicrosService } from '../../channel/micros/services/micros.service';
import { ShortenUrlServiceInterface } from '../../shorten-url/services/shorten-url.service.interface';
import { ShopifyService } from '../../webstore/shopify/services/shopify.service';
import { IntegrationOrder } from '../dto/integration-order.dto';
import { IntegrationPayment } from '../dto/integration-payment.dto';
import { OrderPaymentToCreate } from '../dto/order-payment-to-create.dto';
import { UpdateIntegrationOrderPaymentDto } from '../dto/update-integration-order-payment.dto';
import {
  UpdateIntegrationOrderDto,
  UpdateOrderDispatchDetailsType,
} from '../dto/update-integration-order.dto';
import { IntegrationType } from '../enums/integration-type.enum';
import { OrderToCancel } from '../../../order/dto/order.dto';

@Injectable()
export class EnableService {
  private readonly logger = new Logger(EnableService.name);

  constructor(
    private paymentService: PaymentService,
    private companyService: CompanyService,
    private orderPosService: OrderPosService,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    private aggregatorOrderService: AggregatorOrderService,
    private readonly orderInvoiceService: OrderInvoiceService,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    private readonly orderService: OrderService,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private configService: ConfigService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
    @Inject(ShipmentServiceInterface)
    private shipmentService: ShipmentServiceInterface,
    private branchService: BranchService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandService,
    @Inject(CustomerWebstoreServiceInterface)
    private readonly customerWebstoreService: CustomerWebstoreServiceInterface,
    @Inject(CustomerLoyaltyMemberServiceInterface)
    private readonly customerLoyaltyMemberService: CustomerLoyaltyMemberServiceInterface,
    private menuService: MenuService,
    private menuItemService: MenuItemService,
    private menuCategoryService: MenuCategoryService,
    @Inject(TempCustomerServiceInterface)
    private tempCustomerService: TempCustomerServiceInterface,
    @Inject(CustomerPassLinkServiceInterface)
    private readonly customerPassLinkService: CustomerPassLinkServiceInterface,
    private microsService: MicrosService,
    private readonly shopifyService: ShopifyService,
    @Inject(ShortenUrlServiceInterface)
    private shortenUrlService: ShortenUrlServiceInterface,
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  private logExecutionTime(methodName: string, startTime: number) {
    const endTime = Date.now();
    const executionTime = endTime - startTime;
    this.logger.log(`${methodName} execution time: ${executionTime}ms`);
  }

  async createOrUpdateShopifyProduct(
    storeId: Types.ObjectId,
    shopifyProduct: ShopifyProduct,
  ) {
    const startTime = Date.now();
    try {
      const store = await this.shopifyService.findShopifyStore(storeId);
      const currentUser = this.shopifyService.getCurrentUserForStore(store);
      const menu = await this.menuService.findByStore(store);

      const category = await this.menuCategoryService.findOrCreateWithCache(
        shopifyProduct.category?.name ?? 'No Category',
        menu._id,
        menu.name,
        currentUser,
      );
      const menuItemBulkWriteDto =
        await this.shopifyService.parseMenuItemBulkWriteDto(
          shopifyProduct,
          currentUser,
          menu,
          category._id,
        );

      return await this.menuItemService.bulkWrite(
        store.companyId,
        menuItemBulkWriteDto,
      );
    } finally {
      this.logExecutionTime('createOrUpdateShopifyProduct', startTime);
    }
  }

  async createShopifyOrder(
    storeId: Types.ObjectId,
    createShopifyOrderDto: ShopifyOrder,
  ) {
    const startTime = Date.now();
    try {
      const store = await this.shopifyService.findShopifyStore(storeId);
      const currentUser = this.shopifyService.getCurrentUserForStore(store);

      createShopifyOrderDto.customer.phone =
        this.shopifyService.getShopifyCustomerPhone(
          createShopifyOrderDto,
          store.shopifyConfig?.useGiftOrders,
        );
      const customer: CustomerDocument =
        await this.shopifyService.getShopifyCustomer(
          store.companyId,
          createShopifyOrderDto.customer,
          currentUser,
        );
      const now = moment.utc().utcOffset(3);
      return this.orderPaymentIntegration({
        integrationType: IntegrationType.ORDER,
        company: store.companyId.toString(),
        brand: store.brands[0]._id.toString(),
        createdBy: currentUser,
        orderData: {
          ...this.shopifyService.parseGiftDetails(store, createShopifyOrderDto),
          ...this.shopifyService.mapPaymentGatewayNames(
            createShopifyOrderDto.payment_gateway_names,
          ),
          ...(await this.shopifyService.convertDiscounts(
            createShopifyOrderDto,
            customer,
          )),
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          country_code: customer.country_code,
          email: customer.email,
          pickup_date: now.format('YYYY-DD-MM'),
          pickup_time: now.format('HH:mm'),
          delivery_date: now.format('YYYY-DD-MM'),
          delivery_time: now.format('HH:mm'),
          invoice_number: createShopifyOrderDto.order_number.toString(),
          invoiced_amount: +createShopifyOrderDto.total_line_items_price,
          delivery_amount: createShopifyOrderDto.total_shipping_price_set
            ?.shop_money?.amount
            ? +createShopifyOrderDto.total_shipping_price_set.shop_money.amount
            : 0,
          total_amount: +createShopifyOrderDto.total_price,
          total_amount_after_discount:
            +createShopifyOrderDto.current_total_price,
          discount: +createShopifyOrderDto.current_total_discounts,
          order_remarks: createShopifyOrderDto.note,
          source:
            createShopifyOrderDto.source_name === ShopifySourceName.POS
              ? OrderSource.POS
              : OrderSource.WEBSTORE,
          creationSource: OrderCreationSource.SHOPIFY_WEBHOOK,
          deliveryLocation: store.shopifyConfig.useCustomerPinLocation
            ? this.shopifyService.createSavedLocationToCreate(
                createShopifyOrderDto.shipping_address,
              )
            : null,
          delivery_action: createShopifyOrderDto.shipping_lines[0]
            ? store.shopifyConfig.useCustomerPinLocation
              ? OrderDeliveryAction.DELIVERY_LOCATION
              : OrderDeliveryAction.SEND_SMS
            : OrderDeliveryAction.IN_STORE_PICKUP,
          delivery_type: OrderDeliveryType.urgent,
          items: createShopifyOrderDto.line_items.map((lineItem) =>
            this.shopifyService.convertShopifyLineItemToOrderItem(lineItem),
          ),
          brandId: store.brands[0]._id.toString(),
          company: store.companyId.toString(),
          createdBy: currentUser,
          orderType: 'restaurant',
        },
      });
    } finally {
      this.logExecutionTime('createShopifyOrder', startTime);
    }
  }

  async captureShopifyOrder(
    storeId: Types.ObjectId,
    createShopifyOrderDto: ShopifyOrder,
  ) {
    const startTime = Date.now();
    try {
      const result = await (async () => {
        const store = await this.shopifyService.findShopifyStore(storeId);
        const currentUser = this.shopifyService.getCurrentUserForStore(store);

        createShopifyOrderDto.customer.phone =
          createShopifyOrderDto.shipping_address?.phone ??
          createShopifyOrderDto.customer.phone ??
          createShopifyOrderDto.phone;
        const customer: CustomerDocument =
          await this.shopifyService.getShopifyCustomer(
            store.companyId,
            createShopifyOrderDto.customer,
            currentUser,
          );

        const brand = store.brands[0];

        const delivery_action = createShopifyOrderDto.shipping_lines[0]
          ? store.shopifyConfig.useCustomerPinLocation
            ? OrderDeliveryAction.DELIVERY_LOCATION
            : OrderDeliveryAction.SEND_SMS
          : OrderDeliveryAction.IN_STORE_PICKUP;
        const source =
          createShopifyOrderDto.source_name === ShopifySourceName.POS
            ? CapturedOrderSource.POS
            : CapturedOrderSource.WEBSTORE;
        return this.captureOrder(
          {
            ...this.shopifyService.mapPaymentGatewayNames(
              createShopifyOrderDto.payment_gateway_names,
            ),
            ...(await this.shopifyService.convertDiscounts(
              createShopifyOrderDto,
              customer,
            )),
            delivery_action,
            source,
            creationSource: OrderCreationSource.SHOPIFY_WEBHOOK,
            delivery_amount: createShopifyOrderDto.total_shipping_price_set
              ?.shop_money?.amount
              ? +createShopifyOrderDto.total_shipping_price_set.shop_money
                  .amount
              : 0,
            invoiced_amount: +createShopifyOrderDto.total_line_items_price,
            total_amount: +createShopifyOrderDto.total_price,
            total_amount_after_discount:
              +createShopifyOrderDto.current_total_price,
            total_discount: +createShopifyOrderDto.current_total_discounts,
            amount: +createShopifyOrderDto.total_price,
            companyId: store.companyId,
            customer: {
              countryCode: customer.country_code,
              phoneNumber: customer.phone,
              firstName: customer.first_name,
              lastName: customer.last_name,
              gender: customer.gender,
            },
            date: moment.utc().utcOffset(3).format('YYYY-MM-DD HH:mm'),
            items:
              await this.shopifyService.convertShopifyLineItemsToCapturedItems(
                createShopifyOrderDto.line_items,
                store.companyId,
              ),
            isManuallyCaptured: true,
          },
          store.companyId,
          brand._id,
          currentUser,
        );
      })();
      return result;
    } finally {
      this.logExecutionTime('captureShopifyOrder', startTime);
    }
  }

  async orderPaymentIntegration(orderPaymentToCreate: OrderPaymentToCreate) {
    this.logger.log(
      'Enable Integration: orderPaymentToCreate',
      orderPaymentToCreate,
    );
    // Check if the data is provided in Case of order and payment and orderWithPayment
    if (
      (orderPaymentToCreate.integrationType === IntegrationType.ORDER ||
        orderPaymentToCreate.integrationType ===
          IntegrationType.ORDER_WITH_PAYMENT) &&
      !orderPaymentToCreate.orderData
    ) {
      throw new BadRequestException({
        code: responseCode.MISSING_DATA,
        statusCode: 400,
        message: 'Please Provide the order Data',
      });
    }
    if (
      (orderPaymentToCreate.integrationType === IntegrationType.PAYMENT ||
        orderPaymentToCreate.integrationType ===
          IntegrationType.ORDER_WITH_PAYMENT) &&
      !orderPaymentToCreate.paymentData
    ) {
      throw new BadRequestException({
        code: responseCode.MISSING_DATA,
        statusCode: 400,
        message: 'Please Provide the payment Data',
      });
    }

    // Start Creating The Order
    if (
      orderPaymentToCreate.integrationType === IntegrationType.ORDER &&
      this.isIntegrationOrderData(orderPaymentToCreate.orderData)
    ) {
      orderPaymentToCreate.orderData.createdBy = orderPaymentToCreate.createdBy;
      orderPaymentToCreate.orderData.company = orderPaymentToCreate.company;
      orderPaymentToCreate.orderData.brandId = orderPaymentToCreate.orderData
        .brandId
        ? orderPaymentToCreate.orderData.brandId
        : orderPaymentToCreate.brand
          ? orderPaymentToCreate.brand['_id']
          : undefined;

      const order = await this.orderCreate(orderPaymentToCreate.orderData);

      const orderPojo = order.toObject();

      const {
        tookan_job_id,
        tookan_delivery_track_url,
        tookan_pickup_track_url,
        tookanDeliveryJobId,
        tookanPickupJobIds,
        tookanTaskCancelled,
        ...filteredOrder
      } = orderPojo;

      // Remove unwanted fields from the embedded `company` embedded object
      if (filteredOrder.company && typeof filteredOrder.company === 'object') {
        const {
          number_of_tookan_tasks_own,
          number_of_tookan_tasks_ebutler,
          tookan_driver_id,
          ...filteredCompany
        } = filteredOrder.company as Record<string, any>;
        (filteredOrder.company as Record<string, any>) = filteredCompany;
      }
      if (filteredOrder.branch && typeof filteredOrder.branch === 'object') {
        const { tookan_driver_id, ...filteredBranch } =
          filteredOrder.branch as Record<string, any>;
        (filteredOrder.branch as Record<string, any>) = filteredBranch;
      }
      const trackingUrl = `${this.configService.get('DELIVERY_TRACKING_LINK')}/${order.code}`;
      filteredOrder['trackingUrl'] = await this.shortenUrlService.shortenUrl({
        url: trackingUrl,
        canExpire: false,
      });
      this.logger.log(
        `Tookan Tracking URL Shorten By Enable ${filteredOrder['trackingUrl']}`,
      );
      return filteredOrder;
    } else if (
      orderPaymentToCreate.integrationType === IntegrationType.PAYMENT
    ) {
      orderPaymentToCreate.paymentData.createdBy =
        orderPaymentToCreate.createdBy;
      orderPaymentToCreate.paymentData.company = orderPaymentToCreate.company;
      orderPaymentToCreate.paymentData.brandId = orderPaymentToCreate
        .paymentData.brandId
        ? orderPaymentToCreate.paymentData.brandId
        : orderPaymentToCreate.brand
          ? orderPaymentToCreate.brand['_id']
          : undefined;

      return await this.paymentCreate(orderPaymentToCreate.paymentData);
    } else if (
      orderPaymentToCreate.integrationType ===
        IntegrationType.ORDER_WITH_PAYMENT &&
      this.isIntegrationOrderData(orderPaymentToCreate.orderData)
    ) {
      orderPaymentToCreate.paymentData.createdBy =
        orderPaymentToCreate.createdBy;
      orderPaymentToCreate.orderData.createdBy = orderPaymentToCreate.createdBy;
      orderPaymentToCreate.paymentData.company = orderPaymentToCreate.company;
      orderPaymentToCreate.orderData.company = orderPaymentToCreate.company;
      orderPaymentToCreate.orderData.brandId = orderPaymentToCreate.orderData
        .brandId
        ? orderPaymentToCreate.orderData.brandId
        : orderPaymentToCreate.brand
          ? orderPaymentToCreate.brand['_id']
          : undefined;
      orderPaymentToCreate.orderData.is_gift =
        orderPaymentToCreate['is_gift'] ||
        orderPaymentToCreate.orderData.is_gift;

      orderPaymentToCreate.orderData.recipient_phone =
        orderPaymentToCreate['recipient_phone'] ||
        orderPaymentToCreate.orderData.recipient_phone;

      orderPaymentToCreate.orderData.recipient_name =
        orderPaymentToCreate['recipient_name'] ||
        orderPaymentToCreate.orderData.recipient_name;

      return await this.paymentOrderCreate(
        orderPaymentToCreate.orderData,
        orderPaymentToCreate.paymentData,
      );
    } else if (
      orderPaymentToCreate.integrationType === IntegrationType.ORDER_CAPTURE &&
      !this.isIntegrationOrderData(orderPaymentToCreate.orderData)
    ) {
      orderPaymentToCreate.orderData.companyId = new Types.ObjectId(
        orderPaymentToCreate.company,
      );
      // we added the time concatenation in order to avoid time-zone -3 issue to keep the correct day
      orderPaymentToCreate.orderData.date =
        orderPaymentToCreate.orderData.date + '16:00';

      orderPaymentToCreate.orderData.currentUser =
        orderPaymentToCreate.createdBy;

      return await this.aggregatorOrderService.create(
        orderPaymentToCreate.orderData,
      );
    }
  }

  async getOrderDetails(uniqueIdentifier: string, companyId: Types.ObjectId) {
    if (!companyId) throw new UnauthorizedException('Company Id is required');

    const order = await this.orderService.getDetailsForFront(
      uniqueIdentifier,
      companyId.toHexString(),
    );

    const orderLogs = await this.orderLogService.findAll({
      orderCode: order.code,
    });
    order['logs'] = orderLogs[0]['paginatedResult'];
    order['invoiceUrl'] = `${this.configService.get(
      'FRONTEND_BASE_URL',
    )}/thermal-invoice/download/${order.code}`;
    return order;
  }

  async createShipment(createShipmentDto: CreateShipmentDto) {
    return this.shipmentService.create(createShipmentDto);
  }

  async getShipmentByTrackingNumber(trackingNumber: string) {
    return this.shipmentService.findByTrackingNumber(trackingNumber);
  }

  async getMenus(indexMenuDto: MenuToIndex) {
    return this.menuService.index(indexMenuDto);
  }

  async getMenuItems(menuItemToIndex: MenuItemToIndex) {
    return this.menuItemService.index(menuItemToIndex);
  }

  async getBranches(
    company: Types.ObjectId,
    branches: Types.ObjectId[],
    brandId: Types.ObjectId,
  ) {
    const fetchedBranches = await this.branchService.index({
      company: company as any,
      branches,
      brandId,
    });

    return fetchedBranches.map((branch) => ({
      _id: branch._id,
      name: branch.name,
      location: branch.location,
    }));
  }

  async getCustomerDetails({
    companyId,
    uniqueIdentifier,
    countryCode,
  }: GetCustomerIntegrationDetailsDto): Promise<IntegrationCustomerDetails> {
    this.logger.log('uniqueIdentifier', uniqueIdentifier);
    const company = await this.companyService.findById(companyId);
    const customer = await this.customerReadService.findOne(
      uniqueIdentifier,
      companyId,
      countryCode ?? company?.localization?.countryCode,
    );
    const coupons = await this.couponService.findByCompanyId(companyId, false);
    return this.createIntegrationCustomerDetailsDto(company, customer, coupons);
  }

  async getLoyaltyConfiguration(
    companyId: Types.ObjectId,
    brandId: Types.ObjectId,
  ): Promise<GetIntegrationLoyaltyConfigResponseDto> {
    const loyaltyTiers =
      await this.loyaltyTierService.findByCompanyId(companyId);
    const brand = await this.brandService.findById(brandId);
    const registrationPageConfig = brand.loyaltyProgramConfig?.registrationPage;
    const company = await this.companyService.findById(companyId);

    return {
      loyaltyTiers: loyaltyTiers.map((tier) => tier.toObject()),
      termsAndConditions:
        company.termsAndConditionsUrl ?? 'https://enable.tech',
      brandCountryCode: brand.countryCode,
      brandPhoneNumber: brand.phoneNumber,
      registrationPageWelcomeTextEn: registrationPageConfig?.welcomeTextEn,
      registrationPageWelcomeTextAr: registrationPageConfig?.welcomeTextAr,
    };
  }

  async loyaltyRegister(
    registerLoyaltyCustomerIntegrationDto: RegisterLoyaltyCustomerIntegrationDto,
    brandId: Types.ObjectId,
  ): Promise<RegisterLoyaltyCustomerIntegrationResponseDto> {
    if (!brandId) throw new BadRequestException('Brand Is not found');
    const brand = await this.brandService.findById(brandId);
    const registeredCustomer =
      await this.customerLoyaltyMemberService.registerCustomerInLoyaltyProgram({
        ...registerLoyaltyCustomerIntegrationDto,
        first_name: registerLoyaltyCustomerIntegrationDto.firstName,
        last_name: registerLoyaltyCustomerIntegrationDto.lastName,
        country_code: registerLoyaltyCustomerIntegrationDto.countryCode,
        loyaltyRegistrationBranchId:
          registerLoyaltyCustomerIntegrationDto.branchId,
        source: LRPSource.INTEGRATION_API,
        brandId,
      });
    const customer = await this.customerReadService.findOne(
      registeredCustomer['_id'],
    );
    return {
      customerId: customer._id,
      personalisedLink: this.customerWebstoreService.getOrdableLink(
        customer,
        brandId,
      ),
      iosPassLink: await this.customerPassLinkService.getWalletPassLink(
        customer,
        brand,
        WalletApp.APPLE_WALLET,
        true,
      ),
      androidPassLink: await this.customerPassLinkService.getWalletPassLink(
        customer,
        brand,
        WalletApp.GOOGLE_WALLET,
        true,
      ),
    };
  }

  async captureOrder(
    {
      handoffScheme,
      ...integrationOrderCaptureDto
    }: IntegrationOrderCaptureDto,
    companyId: Types.ObjectId,
    brandId: Types.ObjectId,
    currentUser?: CurrentUser,
  ) {
    if (!companyId)
      throw new UnauthorizedException(`Please provide an API key.`);
    if (!brandId)
      throw new UnauthorizedException(`Please use a brand-specific API key.`);

    if (!integrationOrderCaptureDto?.customer?.firstName) {
      const customer = await this.customerReadService.findByPhoneAndCompanyId(
        integrationOrderCaptureDto.customer.phoneNumber,
        companyId,
        integrationOrderCaptureDto.customer.countryCode,
      );
      if (customer?.first_name?.trim()) {
        integrationOrderCaptureDto.customer = {
          ...integrationOrderCaptureDto.customer,
          firstName: customer.first_name.trim(),
        };
      } else throw new NotFoundException('Customer First Name is Required');
    }

    if (integrationOrderCaptureDto.integrationChannel?.locationRef) {
      const branch = await this.branchService.findByLocationRef(
        companyId,
        integrationOrderCaptureDto.integrationChannel.locationRef,
      );
      if (branch) integrationOrderCaptureDto.branchId = branch._id;
      else
        this.logger.warn(
          `locationRef '${integrationOrderCaptureDto.integrationChannel.locationRef}' was provided but no branch was found.`,
        );
    }

    if (handoffScheme) {
      if (handoffScheme in HandoffSchemeToDeliveryActionMap)
        integrationOrderCaptureDto.delivery_action =
          HandoffSchemeToDeliveryActionMap[handoffScheme];
      else
        this.logger.warn(
          `Received unrecognized handoffScheme ${handoffScheme}`,
        );
    }

    const { couponId, filteredDiscounts } =
      this.orderService.extractCouponInfoForOrdable(
        integrationOrderCaptureDto.discounts,
        integrationOrderCaptureDto.couponId,
      );
    integrationOrderCaptureDto.couponId = couponId;
    integrationOrderCaptureDto.discounts = filteredDiscounts;

    return await this.aggregatorOrderService.create({
      ...integrationOrderCaptureDto,
      companyId,
      brandId,
      currentUser,
    });
  }

  private async orderCreate(integrationOrder: IntegrationOrder) {
    const orderToCreate = this.constructOrderToCreate(integrationOrder, {
      isTest: integrationOrder.is_test,
      callBackUrl: '',
    });
    return await this.orderPosService.create(orderToCreate);
  }

  private toUtcTime(time: string): string {
    // Enable integrators send us the time in HH:mm format in Qatar time.
    // Since Qatar is UTC+3, we need to subtract 3 hours to get the UTC time.
    try {
      const timeParts = time.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = timeParts[1];
      const utcHours = hours - 3 >= 0 ? hours - 3 : hours + 21;
      const utcTime = `${utcHours}:${minutes}`;
      return utcTime;
    } catch (error) {
      // we have to avoid throwing errors when dealing with integrators
      return time;
    }
  }

  private async paymentCreate(integrationPayment: IntegrationPayment) {
    const company = await this.companyService.get_details(
      integrationPayment.company,
    );
    const { phoneNumber, countryCode } =
      this.helperSharedService.getFormattedCountryCodeAndPhoneNumber(
        integrationPayment.customer_phone,
        integrationPayment.country_code,
      );
    const paymentToCreate: PaymentToCreate = {
      amount: integrationPayment.amount,
      callback_url: integrationPayment.callback_url,
      source: OrderSource.WEBSTORE,
      send_sms: integrationPayment.send_sms,
      company: integrationPayment.company,
      is_test: integrationPayment.is_test,
      language: integrationPayment.language,
      customer_name: integrationPayment.customer_name,
      customer_phone: phoneNumber,
      country_code: countryCode,
      company_name: company.name,
      payment_method:
        deprecatedPaymentMethodMapping[integrationPayment.payment_method],
      brandId: integrationPayment.brandId,
    } as any;
    return await this.paymentService.create(
      paymentToCreate,
      integrationPayment.createdBy,
    );
  }

  private async paymentOrderCreate(
    integrationOrder: IntegrationOrder,
    integrationPayment: IntegrationPayment,
  ) {
    const orderToCreate = this.constructOrderToCreate(integrationOrder, {
      isTest: integrationPayment.is_test,
      callBackUrl: integrationPayment.callback_url,
      paymentMethod: OrderPaymentMethod.online,
    });
    const order = await this.orderPosService.create(orderToCreate);
    return await this.paymentService.get_details(order.payment_code);
  }

  private savedAddressToLocationAdapter(
    addressToCreate: any,
  ): SavedLocationToCreate {
    const isNationalAddress = this.isNationalAddress(addressToCreate);

    const locationToCreate: SavedLocationToCreate = {
      nickname: addressToCreate.nickname || addressToCreate.addressName,
      additionalInfo: addressToCreate.additionalInfo || '',
      addressType: SavedLocationAddressType.APARTMENT,
      area: addressToCreate.area || 'Doha',
      city: addressToCreate.city || 'Doha',
      country: addressToCreate.country || 'Qatar',
      buildingName:
        addressToCreate.building_name ||
        addressToCreate.buildingName ||
        addressToCreate.building_number ||
        addressToCreate.buildingNumber ||
        'Customer',
      buildingNumber: this.parseIntOrDefault(
        addressToCreate.building_number || addressToCreate.buildingNumber,
        isNationalAddress ? 0 : undefined,
      ),
      zoneNumber: this.parseIntOrDefault(
        addressToCreate.zone_number || addressToCreate.zoneNumber,
        isNationalAddress ? 0 : undefined,
      ),
      streetNumber: this.parseIntOrDefault(
        addressToCreate.street_number || addressToCreate.streetNumber,
        isNationalAddress ? 0 : undefined,
      ),
      floorNumber: this.parseIntOrDefault(
        addressToCreate.building_house_number || addressToCreate.floorNumber,
        0,
      ),
      latitude: addressToCreate.pin_lat || addressToCreate.lat || 1,
      longitude: addressToCreate.pin_lng || addressToCreate.lng || 1,
      pinLink: addressToCreate.pin_link || addressToCreate.pinLink,
      type: isNationalAddress
        ? SavedLocationType.NATIONAL_ADDRESS
        : SavedLocationType.PIN_LOCATION,
      nearestLandmark: addressToCreate.landmark || '',
      streetName: addressToCreate.office_number || addressToCreate.streetName,
      customerId: undefined,
      unitNumber: this.parseIntOrDefault(addressToCreate.office_number, 0),
    };

    this.ensureValidNumbers(locationToCreate);
    this.handleNationalAddressDefaults(locationToCreate);

    return locationToCreate;
  }

  private isNationalAddress(address: any): boolean {
    return (
      ['nationalAddress', 'NATIONAL_ADDRESS'].includes(address.locationType) ||
      ['nationalAddress', 'NATIONAL_ADDRESS'].includes(address.addressType) ||
      address.type === SavedLocationType.NATIONAL_ADDRESS
    );
  }

  private parseIntOrDefault(value: any, defaultValue: number): number {
    const parsed = parseInt(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  private ensureValidNumbers(location: SavedLocationToCreate): void {
    const numericFields = [
      'streetNumber',
      'buildingNumber',
      'zoneNumber',
      'unitNumber',
      'floorNumber',
    ];
    numericFields.forEach((field) => {
      if (isNaN(location[field])) {
        location[field] = 0;
      }
    });
  }

  private handleNationalAddressDefaults(location: SavedLocationToCreate): void {
    if (location.type === SavedLocationType.NATIONAL_ADDRESS) {
      ['streetNumber', 'buildingNumber', 'zoneNumber'].forEach((field) => {
        if (!location[field]) {
          location[field] = 1;
        }
      });
    }
  }

  private createIntegrationCustomerDetailsDto(
    company: CompanyDocument,
    customer: CustomerDocument,
    coupons: CouponDocument[],
  ): IntegrationCustomerDetails {
    const {
      loyaltyStatus,
      loyaltyTier,
      rewards,
      first_name,
      last_name,
      title,
      gender,
      phone,
      country_code,
      email,
      _id,
      loyaltyPoints,
    } = customer;
    const isMember = loyaltyStatus === LoyaltyStatus.MEMBER;

    const discounts = this.getDiscounts(isMember, coupons, loyaltyTier);
    const loyaltyBenefits = this.getLoyaltyBenefits(loyaltyTier);
    const customerFullName = last_name
      ? `${first_name} ${last_name}`
      : first_name;

    return {
      customerName: customerFullName,
      customerId: _id.toHexString(),
      title,
      gender,
      phoneNumber: phone,
      countryCode: country_code,
      email,
      discounts,
      loyaltyBenefits,
      loyaltyTier:
        isMember && loyaltyTier
          ? this.createIntegrationLoyaltyTierDto(company, loyaltyTier)
          : {},
      coupons: isMember ? coupons.map(this.createIntegrationCouponDto) : [],
      loyaltyPoints,
      loyaltyStatus,
      rewards:
        isMember && rewards ? rewards.map(this.createIntegrationRewardDto) : [],
    };
  }

  private createIntegrationRewardDto(reward: EarnedReward): IntegrationReward {
    return {
      benefit: reward.benefit,
      amount:
        reward.benefit === PunchCardBenefit.PERCENT_DISCOUNT
          ? reward.amount
          : undefined,
      menuItemNameEn:
        reward.benefit === PunchCardBenefit.MENU_ITEM
          ? reward.menuItem.nameEn
          : undefined,
      microsDiscountId: reward.microsDiscountId,
      masterMenuItemId:
        reward.benefit === PunchCardBenefit.MENU_ITEM
          ? reward.menuItem.masterMenuItemId
          : undefined,
    };
  }

  private getDiscounts(
    isMember: boolean,
    coupons: CouponDocument[],
    loyaltyTier?: LoyaltyTier | EmbeddedTierDto,
  ): number[] {
    const discounts: number[] = [];

    if (isMember) {
      discounts.push(
        ...coupons.flatMap((coupon) =>
          coupon.benefits.map((benefit) => {
            switch (benefit.type) {
              case BenefitType.PERCENTAGE_DISCOUNT:
                return benefit.value / 100;
              case BenefitType.FIXED_DISCOUNT:
                return benefit.value;
              default:
                throw new UnreachableError(benefit.type);
            }
          }),
        ),
      );
    }

    if (loyaltyTier) {
      discounts.push(loyaltyTier.percentDiscount / 100);
    }

    return discounts;
  }

  private getLoyaltyBenefits(
    loyaltyTier?: LoyaltyTier | EmbeddedTierDto,
  ): string[] {
    if (loyaltyTier && loyaltyTier.freeDelivery) {
      return ['freeDelivery', 'giftItem'];
    } else {
      return [];
    }
  }

  private createIntegrationLoyaltyTierDto(
    company: CompanyDocument,
    loyaltyTier: EmbeddedTierDto,
  ): IntegrationLoyaltyTier {
    if (!loyaltyTier) return undefined;
    const { orderValueThreshold } = company.loyaltyProgramConfig;
    return {
      name: loyaltyTier.nameEn,
      orderValueThreshold,
      tierDiscount: loyaltyTier.percentDiscount / 100,
      ordersRateThreshold: loyaltyTier.orderRateThreshold,
      amountSpentThreshold: loyaltyTier.amountSpentThreshold,
      pointsRateThreshold: loyaltyTier.pointsRateThreshold,
      isVipTier: loyaltyTier.isVipTier,
      freeDelivery: loyaltyTier.freeDelivery,
      microsDiscountId: loyaltyTier.microsDiscountId,
    };
  }

  private createIntegrationCouponDto(
    coupon: CouponDocument,
  ): IntegrationCoupon {
    const { _id, benefits, loyaltyPointCost, microsDiscountId } = coupon;
    const flatDiscountAmount =
      benefits[0].type === BenefitType.FIXED_DISCOUNT
        ? benefits[0].value
        : undefined;

    return {
      _id,
      loyaltyPointCost,
      microsDiscountId,
      flatDiscountAmount,
    };
  }

  private isIntegrationOrderData(
    orderData: IntegrationOrder | AggregatorOrderToCreate,
  ): orderData is IntegrationOrder {
    return 'first_name' in orderData;
  }

  private constructOrderToCreate(
    integrationOrder: IntegrationOrder,
    {
      isTest,
      callBackUrl,
      paymentMethod,
    }: {
      isTest: boolean;
      callBackUrl: string;
      paymentMethod?: OrderPaymentMethod;
    },
  ): OrderPosToCreate {
    const { phoneNumber, countryCode } =
      this.helperSharedService.getFormattedCountryCodeAndPhoneNumber(
        integrationOrder.phone,
        integrationOrder.country_code,
      );
    const recipientPhoneNumber = integrationOrder.is_gift
      ? this.helperSharedService.getFormattedCountryCodeAndPhoneNumber(
          integrationOrder.recipient_phone,
          integrationOrder.recipient_country_code,
        )
      : undefined;

    return {
      orderIsAlreadyPaid: false,
      first_name: integrationOrder.first_name,
      last_name: integrationOrder.last_name,
      phone: phoneNumber,
      is_gift: integrationOrder.is_gift,
      isCustomerUpdatable: true,
      autoAssign: false,
      country_code: countryCode,
      recipient_country_code: recipientPhoneNumber
        ? recipientPhoneNumber.countryCode
        : undefined,
      recipient_name: integrationOrder.recipient_name,
      recipient_phone: recipientPhoneNumber
        ? recipientPhoneNumber.phoneNumber
        : undefined,
      source: Object.values(OrderSource).includes(integrationOrder.source)
        ? integrationOrder.source
        : OrderSource.WEBSTORE,
      creationSource:
        integrationOrder.creationSource ??
        OrderCreationSource.ORDER_PAYMENT_INTEGRATION_API,
      order_remarks: integrationOrder.order_remarks,
      delivery_action: integrationOrder.delivery_action,
      deliveryLocation: integrationOrder.delivery_address
        ? this.savedAddressToLocationAdapter(integrationOrder.delivery_address)
        : integrationOrder.deliveryLocation,
      invoiced_amount: integrationOrder.invoiced_amount,
      invoice_number: integrationOrder.invoice_number,
      total_amount: integrationOrder.total_amount,
      total_amount_after_discount: integrationOrder.total_amount_after_discount,
      delivery_amount: integrationOrder.delivery_amount
        ? integrationOrder.delivery_amount
        : 0,
      delivery_date: integrationOrder.delivery_date,
      delivery_time: this.toUtcTime(integrationOrder.delivery_time),
      delivery_type: integrationOrder.delivery_type,
      delivery_slot_from: this.toUtcTime(integrationOrder.delivery_slot_from),
      delivery_slot_to: this.toUtcTime(integrationOrder.delivery_slot_to),
      email: integrationOrder.email,
      payment_method: paymentMethod
        ? paymentMethod
        : integrationOrder.payment_method,
      prepaidBy: integrationOrder.prepaidBy,
      items: integrationOrder.items,
      pickup_date: integrationOrder.pickup_date,
      pickup_time: this.toUtcTime(integrationOrder.pickup_time),
      callback_url: callBackUrl,
      is_test: integrationOrder.is_test || isTest,
      current_user: integrationOrder.createdBy,
      company: integrationOrder.company,
      discount: integrationOrder.discount,
      discounts:
        integrationOrder.discounts ??
        this.orderInvoiceService.convertLegacyDiscountToDiscounts(
          integrationOrder.discount,
        ),
      benefits: integrationOrder.benefits,
      couponId: integrationOrder.couponId,
      status: OrderStatusEnum.UNASSIGNED,
      orderType: integrationOrder.orderType,
      is_secret: integrationOrder.is_secret,
      cardMessage: integrationOrder.cardMessage,
      language: integrationOrder.language
        ? integrationOrder.language
        : Language.english,
      dispatch_type: integrationOrder.delivery_type,
      paymentCode: integrationOrder.paymentCode,
      branch: integrationOrder?.enable_branch_id
        ? integrationOrder.enable_branch_id
        : '',
      brandId: integrationOrder.brandId,
      bigcommerceOrderId: integrationOrder.bigcommerceOrderId,
    };
  }

  async updateIntegrationOrderPayment(
    updateIntegrationOrderPaymentDto: UpdateIntegrationOrderPaymentDto,
  ) {
    if (
      [IntegrationType.ORDER, IntegrationType.ORDER_WITH_PAYMENT].includes(
        updateIntegrationOrderPaymentDto.integrationType,
      ) &&
      updateIntegrationOrderPaymentDto.order
    ) {
      return this.updateIntegrationOrder(
        updateIntegrationOrderPaymentDto.order,
      );
    }
    throw new BadRequestException(
      `Integration Type ${updateIntegrationOrderPaymentDto.integrationType} is Not Supported`,
    );
  }

  private async updateIntegrationOrder({
    orderId,
    deliveryLocation,
    deliveryLocationId,
    ...rest
  }: UpdateIntegrationOrderDto) {
    const order = await this.orderService.findByIdOrCode(orderId);
    if (deliveryLocationId || deliveryLocation) {
      await this.orderService.update_delivery_location({
        order_id: orderId,
        location: deliveryLocation,
        locationId: deliveryLocationId,
      });
    }

    if (
      rest.deliveryDate ||
      rest.deliveryTime ||
      rest.pickupDate ||
      rest.pickupTime
    ) {
      await this.updateOrderDispatchDetails(order, { ...rest, orderId });
    }
  }

  private async updateOrderDispatchDetails(
    order: Order,
    {
      orderId,
      deliveryDate,
      deliveryTime,
      pickupTime,
      pickupDate,
    }: UpdateOrderDispatchDetailsType,
  ) {
    await this.orderService.editOrderDispatch(
      {
        order: orderId,
        delivery_date: deliveryDate || order.delivery_date.toDateString(),
        delivery_time: deliveryTime || order.delivery_time,
        delivery_type: order.delivery_type,
        pickup_date: pickupDate || order.pickup_date.toDateString(),
        deliveryMethod: order.deliveryMethod,
        pickup_time: pickupTime,
        delivery_action: order.delivery_action,
        driver_id: undefined,
        delivery_slot_id: undefined,
        current_user: undefined,
        traceId: undefined,
        transport_type: order.transport_type,
      },
      order.company,
    );
  }

  async changeShipmentStatus(
    shipmentRef: string,
    status: ShipmentStatus,
  ): Promise<void> {
    await this.shipmentService.changeShipmentStatus(shipmentRef, status);
  }

  async updateTempCustomer({
    salesOrderId,
    ...updates
  }: IntegrationUpdateTempCustomerDto): Promise<TempCustomerDocument> {
    return await this.tempCustomerService.updateOne(salesOrderId, updates);
  }

  async syncMicrosCheck(
    postMicrosOrderDto: PostMicrosOrderDto,
    companyId: Types.ObjectId,
    brandId: Types.ObjectId,
    currentUser: CurrentUser,
  ): Promise<void> {
    await this.microsService.syncCheck(
      postMicrosOrderDto,
      companyId,
      brandId,
      currentUser,
    );
  }

  async cancelOrder(orderToCancel: OrderToCancel): Promise<void> {
    orderToCancel.comment = 'cancel integration order';
    await this.orderService.cancelOrder(orderToCancel);
  }
}
