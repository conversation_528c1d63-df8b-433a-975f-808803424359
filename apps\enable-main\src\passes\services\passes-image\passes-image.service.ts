import {
  areObjectIdsEqual,
  BarConfig,
  BrandDocument,
  CompanyDocument,
  CustomerDocument,
  EmbeddedPunchCardDto,
  forEachAsync,
  GenerateStampImageDto,
  IconMeterConfig,
  Image,
  IMAGE_URLS,
  LoggerService,
  LoyaltyTier,
  LoyaltyTierDocument,
  LoyaltyTierMilestone,
  LoyaltyTierProgramProgress,
  LoyaltyTierSearchType,
  mapAsync,
  MinibarConfig,
  passBarDisplayTextGetters,
  PassConfig,
  PunchCardProgress,
  PunchCardStamp,
  PunchCardStampType,
  SpecialStamp,
  StampRowContext,
  StampsConfig,
  StatefulTierStamp,
  StripImageConfig,
  TierLevellingUpMethod,
  TierStamp,
} from '@app/shared-stuff';
import {
  createCanvas,
  GlobalFonts,
  loadImage,
  SKRSContext2D,
} from '@napi-rs/canvas';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerPassServiceInterface } from '../../../customer/modules/customer-pass/customer-pass.service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerTierInfoServiceInterface } from '../../../customer/modules/customer-tier-info/customer-tier-info.service.interface';
import { LoyaltyTierIndexService } from '../../../loyalty-tier/modules/loyalty-tier-index/loyalty-tier-index.service';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { CompletedPunchCardService } from '../../../punch-card/modules/completed-punch-card/completed-punch-card.service';
import { PunchCardReadService } from '../../../punch-card/modules/punch-card-read/punch-card-read.service';
import { LoadedImage } from '../../dto/loaded-image.dto';
import { StripIcon } from '../../enums/strip-icon.enum';
import { PassConfigService } from '../pass-config/pass-config.service';
import { PassesImageServiceInterface } from './passes-image.service.interface';

@Injectable()
export class PassesImageService implements PassesImageServiceInterface {
  private readonly loggerService = new LoggerService(PassesImageService.name);

  private readonly WIDTH = 1125;
  private readonly HEIGHT = 432;

  private readonly ICON_PADDING = 25;
  private readonly BORDER_WIDTH = 10;
  private readonly STROKE_WIDTH = 3;
  private readonly MIN_HORIZONTAL_MARGIN = 75;
  private readonly MIN_VERTICAL_MARGIN = 25;

  private readonly MAX_ICONS = 24;
  private readonly MAX_ICON_SIZE = 148;

  private readonly PROGRESS_BAR_AREA_HEIGHT = 86;
  private readonly PROGRESS_BAR_PADDING_TOP = 16;
  private readonly PROGRESS_BAR_HEIGHT = 40;
  private readonly PROGRESS_BAR_WIDTH = this.WIDTH * 0.5;

  private readonly BAR_ONLY_LAYOUT_WIDTH = this.WIDTH * 0.7;
  private readonly BAR_ONLY_LAYOUT_HEIGHT = 60;
  private readonly BAR_ONLY_LAYOUT_TOP = 130;
  private readonly BAR_ONLY_LAYOUT_DISPLAY_SIZE =
    this.BAR_ONLY_LAYOUT_HEIGHT + (this.STROKE_WIDTH - 1) * 2;

  private readonly ICON_METER_BACKGROUND_WIDTH = this.WIDTH * 0.8;
  private readonly ICON_METER_BACKGROUND_HEIGHT = 43;
  private readonly ICON_METER_ICON_MARGIN_X = 5;
  private readonly ICON_METER_ICON_MARGIN_Y = 5;
  private readonly ICON_METER_ICON_PADDING = 2;

  private readonly FONT_FAMILY = 'Avancement';
  private readonly FONT_SIZE_M = 30;
  private readonly FONT_SIZE_S = 25;

  constructor(
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    private readonly loyaltyTierIndexService: LoyaltyTierIndexService,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerPassServiceInterface)
    private readonly customerPassService: CustomerPassServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerTierInfoService: CustomerTierInfoServiceInterface,
    private readonly configService: ConfigService,
    private readonly companyService: CompanyService,
    private readonly punchCardService: PunchCardReadService,
    private readonly completedPunchCardService: CompletedPunchCardService,
    private readonly passConfigService: PassConfigService,
  ) {
    GlobalFonts.registerFromPath(
      './assets/fonts/Avancement2020-Regular(1-5).otf',
      'Avancement',
    );
  }

  generateImageUrl(customer: CustomerDocument, brand: BrandDocument): string {
    const imageUrl = new URL(
      '/v1/stamps.png',
      this.configService.get('HOST_URL'),
    );
    imageUrl.searchParams.set('customerId', customer._id.toString());
    imageUrl.searchParams.set('brandId', brand._id.toString());
    imageUrl.searchParams.set('nonce', Date.now().toString());
    return imageUrl.toString();
  }

  async generateStreamableImage({
    customerId,
    brandId,
  }: GenerateStampImageDto): Promise<Buffer> {
    const customer = await this.customerReadService.findById(customerId);
    const company = await this.companyService.findById(customer.company);
    const loyaltyTierProgramProgress =
      await this.customerTierInfoService.getLoyaltyTierProgramProgress(
        customer,
        company,
      );
    const loyaltyTier = customer.loyaltyTier
      ? await this.loyaltyTierReadService.findById(customer.loyaltyTier._id)
      : null;
    const passConfig = await this.passConfigService.getPassConfig(
      company._id,
      brandId,
      loyaltyTier?._id,
    );

    await this.customerPassService.applyPassGenerationPreFunctions(customer);
    const image = await this.generateImage(
      customer,
      loyaltyTierProgramProgress,
      passConfig,
      company,
    );
    return image;
  }

  async generateImage(
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
    passConfig: PassConfig,
    company: CompanyDocument,
  ): Promise<Buffer> {
    const canvas = createCanvas(this.WIDTH, this.HEIGHT);
    const ctx = canvas.getContext('2d');

    await this.drawBackground(ctx, passConfig?.stripImageConfig);

    const hasTiers =
      company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      !customer.loyaltyTier?.isVipTier;
    const hasPunchCards = company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;
    const isTiersOnly = hasTiers && !hasPunchCards;
    const isTiersAndPunchCards = hasTiers && hasPunchCards;

    const tierLevellingUpMethod =
      company?.loyaltyProgramConfig?.tierLevellingUpMethod;

    const isTierOnlyLayout =
      isTiersOnly &&
      [
        TierLevellingUpMethod.AMOUNT_SPENT,
        TierLevellingUpMethod.POINTS_RATE,
      ].includes(tierLevellingUpMethod);

    const useAlternativeLayout =
      isTierOnlyLayout ||
      (hasTiers && passConfig?.stripImageConfig?.stampsConfig?.isHidden);

    const useBarOnlyLayout =
      useAlternativeLayout &&
      !passConfig?.stripImageConfig?.barConfig?.isHidden;

    const useIconMeterLayout =
      useAlternativeLayout && passConfig?.stripImageConfig?.barConfig?.isHidden;

    const isOrdersAndAmountSpentTiers =
      isTiersOnly && tierLevellingUpMethod === TierLevellingUpMethod.BOTH;

    const isPunchCardAndTiers =
      isTiersAndPunchCards &&
      [
        TierLevellingUpMethod.AMOUNT_SPENT,
        TierLevellingUpMethod.POINTS_RATE,
      ].includes(tierLevellingUpMethod);

    const isMinibarLayout = isOrdersAndAmountSpentTiers || isPunchCardAndTiers;
    const isMinibarBarDrawn =
      isMinibarLayout &&
      !passConfig?.stripImageConfig?.isStampAndBarLayout &&
      !passConfig?.stripImageConfig?.minibarConfig?.isHidden &&
      !passConfig?.stripImageConfig?.stampsConfig?.isHidden;

    const stampsHeight = passConfig?.stripImageConfig?.isStampAndBarLayout
      ? this.HEIGHT / 2
      : isMinibarBarDrawn
        ? this.HEIGHT - this.PROGRESS_BAR_AREA_HEIGHT
        : this.HEIGHT;

    if (useBarOnlyLayout || passConfig?.stripImageConfig?.isStampAndBarLayout)
      await this.drawBarOnlyLayout(
        ctx,
        customer,
        loyaltyTierProgramProgress,
        passConfig?.stripImageConfig?.barConfig,
        company,
        passConfig?.stripImageConfig?.isStampAndBarLayout ? 140 : 0,
      );
    else if (useIconMeterLayout)
      await this.drawIconMeterLayout(
        ctx,
        customer,
        loyaltyTierProgramProgress,
        passConfig?.stripImageConfig,
        company,
      );

    if (
      isTiersOnly &&
      !useBarOnlyLayout &&
      !passConfig?.stripImageConfig?.stampsConfig?.isHidden
    )
      await this.drawStampsForTiers(
        ctx,
        customer,
        loyaltyTierProgramProgress,
        passConfig?.stripImageConfig.stampsConfig,
        stampsHeight,
      );
    else if (
      hasPunchCards &&
      !useBarOnlyLayout &&
      !passConfig?.stripImageConfig?.stampsConfig?.isHidden
    )
      await this.drawStampsForPunchCard(
        ctx,
        customer,
        passConfig?.stripImageConfig?.stampsConfig,
        stampsHeight,
      );

    if (isMinibarBarDrawn)
      await this.drawMinibar(
        ctx,
        customer,
        passConfig?.stripImageConfig?.minibarConfig,
        company,
        passConfig?.passTemplate?.backgroundColor,
      );

    return canvas.toBuffer('image/png');
  }

  private async drawIconMeterLayout(
    ctx: SKRSContext2D,
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
    stripImageConfig: StripImageConfig,
    company: CompanyDocument,
  ) {
    this.loggerService.log('Drawing Icon Meter Layout');

    const tiers = await this.getTiers(customer, loyaltyTierProgramProgress);
    if (!tiers || tiers.length === 0)
      return this.loggerService.warn(
        'Company has no tiers to draw progress bar with.',
        { customer, loyaltyTierProgramProgress, tiers },
      );

    const nextMilestone = await this.customerTierInfoService.getNextMilestone(
      customer,
      company,
    );

    const milestone =
      nextMilestone ??
      (await this.customerTierInfoService.getLastMilestone(customer, company));

    if (!milestone)
      return this.loggerService.warn(
        'Customer has no milestone to draw icon meter with.',
        { customer, loyaltyTierProgramProgress, nextMilestone, milestone },
      );

    const iconMeterX = this.WIDTH / 2 - this.ICON_METER_BACKGROUND_WIDTH / 2;
    const iconMeterY = this.HEIGHT / 2 - this.ICON_METER_BACKGROUND_HEIGHT / 2;

    const iconMeterConfig = stripImageConfig?.iconMeterConfig;
    this.drawIconMeterBackground(ctx, iconMeterConfig, iconMeterX, iconMeterY);

    await this.drawIconMeterIcons(
      ctx,
      iconMeterConfig,
      milestone,
      iconMeterX,
      iconMeterY,
    );

    const textColor =
      iconMeterConfig?.tierTextColor?.[customer.loyaltyTier?._id.toString()] ??
      iconMeterConfig?.textColor ??
      '#fff';

    const progressText = `${milestone.threshold - milestone.distanceToThreshold} / ${milestone.threshold} ${milestone.currencySymbol}`;
    this.writeIconMeterProgressText(
      ctx,
      progressText,
      textColor,
      iconMeterX,
      iconMeterY,
    );

    await this.drawIconMeterMilestone(
      ctx,
      customer,
      iconMeterConfig,
      milestone,
      iconMeterX,
      iconMeterY,
    );

    const startX = this.computeStartX(stripImageConfig);
    await this.drawTierReference(
      ctx,
      startX,
      iconMeterConfig,
      textColor,
      tiers,
      milestone,
      company.loyaltyProgramConfig.tierLevellingUpMethod ===
        TierLevellingUpMethod.AMOUNT_SPENT
        ? 'amountSpentThreshold'
        : 'pointsRateThreshold',
    );

    await this.writeExpiryText(ctx, startX, customer, company, textColor);
  }

  private drawIconMeterBackground(
    ctx: SKRSContext2D,
    iconMeterConfig: IconMeterConfig,
    iconMeterX: number,
    iconMeterY: number,
  ) {
    ctx.fillStyle = iconMeterConfig?.meterBackgroundColor ?? '#000000';
    this.drawBar(
      ctx,
      iconMeterX,
      iconMeterY,
      this.ICON_METER_BACKGROUND_HEIGHT,
      this.ICON_METER_BACKGROUND_WIDTH,
    );
  }

  private async drawIconMeterIcons(
    ctx: SKRSContext2D,
    iconMeterConfig: IconMeterConfig,
    milestone: LoyaltyTierMilestone,
    iconMeterX: number,
    iconMeterY: number,
  ) {
    const numIcons = iconMeterConfig?.numIcons ?? 16;
    const valuePerIcon = milestone.threshold / numIcons;
    const progressValue = milestone.threshold - milestone.distanceToThreshold;
    const numFilledIcons = Math.floor(progressValue / valuePerIcon);
    const iconSizeX =
      (this.ICON_METER_BACKGROUND_WIDTH -
        this.ICON_METER_ICON_PADDING * numIcons -
        this.ICON_METER_ICON_MARGIN_X * 2) /
      numIcons;
    const iconSizeY =
      this.ICON_METER_BACKGROUND_HEIGHT - this.ICON_METER_ICON_MARGIN_Y * 2;
    const iconWidth = iconSizeX + this.ICON_METER_ICON_PADDING;

    const filledIcon = await loadImage(
      iconMeterConfig?.filledIcon?.url || IMAGE_URLS.FILLED_ICON_METER_ICON,
    );

    const emptyIcon = await loadImage(
      iconMeterConfig?.emptyIcon?.url || IMAGE_URLS.EMPTY_ICON_METER_ICON,
    );

    const tierIconUrl =
      iconMeterConfig?.tierIcons?.[milestone._id.toString()]?.url;

    for (let i = 0; i < numIcons; i++) {
      const isTierIcon = i === numIcons - 1 && tierIconUrl;
      const icon = isTierIcon
        ? await loadImage(tierIconUrl)
        : i < numFilledIcons
          ? filledIcon
          : emptyIcon;

      ctx.drawImage(
        icon,
        iconMeterX + this.ICON_METER_ICON_MARGIN_X + iconWidth * i,
        iconMeterY + this.ICON_METER_ICON_MARGIN_Y,
        iconSizeX,
        iconSizeY,
      );

      if (i === numFilledIcons) {
        const fillPercent = (progressValue % valuePerIcon) / valuePerIcon;
        ctx.drawImage(
          filledIcon,
          0,
          0,
          filledIcon.width * fillPercent,
          filledIcon.height,
          iconMeterX + this.ICON_METER_ICON_MARGIN_X + iconWidth * i,
          iconMeterY + this.ICON_METER_ICON_MARGIN_Y,
          iconSizeX * fillPercent,
          iconSizeY,
        );
      }
    }
  }

  private writeIconMeterProgressText(
    ctx: SKRSContext2D,
    progressText: string,
    textColor: string,
    iconMeterX: number,
    iconMeterY: number,
  ) {
    ctx.textBaseline = 'top';
    ctx.fillStyle = textColor;
    ctx.font = `bold ${this.FONT_SIZE_M}px ${this.FONT_FAMILY}`;
    ctx.fillText(
      progressText,
      iconMeterX + this.ICON_METER_ICON_MARGIN_X,
      iconMeterY +
        this.ICON_METER_BACKGROUND_HEIGHT +
        this.ICON_METER_ICON_MARGIN_Y * 2,
    );
  }

  private async drawIconMeterMilestone(
    ctx: SKRSContext2D,
    customer: CustomerDocument,
    iconMeterConfig: IconMeterConfig,
    milestone: LoyaltyTierMilestone,
    iconMeterX: number,
    iconMeterY: number,
  ) {
    const isMaintainMilestone =
      customer.loyaltyTier?._id.toString() === milestone._id.toString();
    if (isMaintainMilestone) return;

    const tierMilestone =
      iconMeterConfig?.tierMilestones?.[milestone._id.toString()];
    if (!tierMilestone) return;

    const tierMilestoneImage = await loadImage(tierMilestone.url);
    ctx.drawImage(
      tierMilestoneImage,
      iconMeterX +
        this.ICON_METER_BACKGROUND_WIDTH -
        tierMilestoneImage.width -
        this.ICON_METER_ICON_MARGIN_X * 2,
      iconMeterY +
        this.ICON_METER_BACKGROUND_HEIGHT +
        this.ICON_METER_ICON_MARGIN_Y * 2,
    );
  }

  private async drawTierReference(
    ctx: SKRSContext2D,
    startX: number,
    iconMeterConfig: IconMeterConfig,
    textColor: string,
    tiers: LoyaltyTierDocument[],
    milestone: LoyaltyTierMilestone,
    thresholdKey: keyof LoyaltyTier,
  ) {
    ctx.fillStyle = textColor;
    ctx.font = `${this.FONT_SIZE_S}px ${this.FONT_FAMILY}`;

    let tiersWidth = 0;
    for (let i = 0; i < tiers.length; i++) {
      const tier = tiers[i];
      const tierIcon = iconMeterConfig?.tierIcons?.[tier._id.toString()];
      if (!tierIcon) continue;

      const tierImage = await loadImage(tierIcon.url);

      const x = startX + this.FONT_SIZE_S + tiersWidth;
      ctx.drawImage(
        tierImage,
        x,
        this.HEIGHT - this.FONT_SIZE_S * 1.2 - 5 - tierImage.height / 2,
      );

      const firstLine = `${tier.nameEn} starts at`;
      const firstLineMetrics = ctx.measureText(firstLine);
      ctx.fillText(
        firstLine,
        x + tierImage.width + 5,
        this.HEIGHT - this.FONT_SIZE_S * 2.2 - 5,
      );

      const secondLine = `${tier[thresholdKey]} ${milestone.currencySymbol}`;
      const secondLineMetrics = ctx.measureText(secondLine);
      ctx.fillText(
        secondLine,
        x + tierImage.width + 5,
        this.HEIGHT - this.FONT_SIZE_S * 1.2 - 5,
      );

      tiersWidth +=
        tierImage.width +
        this.FONT_SIZE_S * 2 +
        Math.max(firstLineMetrics.width, secondLineMetrics.width);
    }
  }

  private async writeExpiryText(
    ctx: SKRSContext2D,
    startX: number,
    customer: CustomerDocument,
    company: CompanyDocument,
    textColor: string,
  ) {
    if (!customer.loyaltyTier) return;

    const validTill = await this.customerTierInfoService.getTierValidTill(
      customer,
      company,
    );
    if (!validTill) return;

    ctx.fillStyle = textColor;
    ctx.font = `${this.FONT_SIZE_S}px ${this.FONT_FAMILY}`;
    const expiryTextLine1 = `${customer.loyaltyTier.nameEn} Expiry Date:`;
    const expiryTextLine1Metrics = ctx.measureText(expiryTextLine1);
    const expiryTextLine2 = moment.utc(validTill).format('YYYY-MM-DD');

    ctx.fillText(
      expiryTextLine1,
      this.WIDTH - startX - expiryTextLine1Metrics.width - this.FONT_SIZE_S,
      this.HEIGHT - this.FONT_SIZE_S * 2.2 - 5,
    );
    ctx.fillText(
      expiryTextLine2,
      this.WIDTH - startX - expiryTextLine1Metrics.width - this.FONT_SIZE_S,
      this.HEIGHT - this.FONT_SIZE_S * 1.2 - 5,
    );
  }

  private async drawBarOnlyLayout(
    ctx: SKRSContext2D,
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
    barConfig: BarConfig,
    company: CompanyDocument,
    yOffset = 0,
  ) {
    this.loggerService.log('Drawing Only Bar Layout');

    const tiers = await this.getTiers(customer, loyaltyTierProgramProgress);
    if (!tiers || tiers.length === 0)
      return this.loggerService.warn(
        'Customer has no tiers to draw progress bar with.',
        { customer, loyaltyTierProgramProgress, tiers },
      );

    const isAmountSpentBar =
      company.loyaltyProgramConfig.tierLevellingUpMethod ===
      TierLevellingUpMethod.AMOUNT_SPENT;
    const threshold: keyof LoyaltyTierDocument = isAmountSpentBar
      ? 'amountSpentThreshold'
      : 'pointsRateThreshold';

    const defaultTierStamp = barConfig?.tierMilestones?.find(
      (tierStamp) => !tierStamp.tierId,
    );

    const lastTier = tiers.reduce((max, tier) =>
      tier?.[threshold] > max?.[threshold] ? tier : max,
    );
    const lastTierStamp =
      barConfig?.tierMilestones?.find(({ tierId }) =>
        areObjectIdsEqual(tierId, lastTier._id),
      ) ?? defaultTierStamp;
    const lastTierScale = lastTierStamp?.stamp?.scale ?? 1;
    const baseSize = this.BAR_ONLY_LAYOUT_DISPLAY_SIZE;
    const scaledSize = this.BAR_ONLY_LAYOUT_DISPLAY_SIZE * lastTierScale;
    const extraRightSpace = Math.max(0, (scaledSize - baseSize) / 2);

    const progressValue = isAmountSpentBar
      ? loyaltyTierProgramProgress.amountSpent
      : loyaltyTierProgramProgress.pointsRate;
    const progress = progressValue / lastTier[threshold];

    const progressBarTop =
      this.BAR_ONLY_LAYOUT_TOP + (barConfig?.yOffset || 0) + yOffset;
    const progressBarLeft =
      (this.WIDTH - this.BAR_ONLY_LAYOUT_WIDTH) / 2 - extraRightSpace;

    this.drawProgressBars(
      ctx,
      barConfig?.outlineColor,
      barConfig?.fillColor,
      progressBarLeft,
      progressBarTop,
      this.BAR_ONLY_LAYOUT_HEIGHT,
      this.BAR_ONLY_LAYOUT_WIDTH,
      progress,
    );

    await this.drawTierMilestones(
      ctx,
      tiers,
      barConfig,
      defaultTierStamp,
      threshold,
      lastTier,
      progressBarTop,
      company,
    );

    if (barConfig?.hideEndText) return;
    const textHeight = progressBarTop + this.BAR_ONLY_LAYOUT_HEIGHT / 2 + 10;
    const textMargin = 4;
    const progressBarEnd = this.WIDTH / 2 + this.BAR_ONLY_LAYOUT_WIDTH / 2;
    this.writeEndText(
      ctx,
      barConfig?.textColor,
      await this.customerTierInfoService.getNextMilestone(customer, company),
      progressBarEnd + textMargin + extraRightSpace,
      textHeight,
    );
  }

  private async drawTierMilestones(
    ctx: SKRSContext2D,
    tiers: LoyaltyTierDocument[],
    barConfig: BarConfig,
    defaultTierStamp: TierStamp,
    threshold: string,
    lastTier: LoyaltyTierDocument,
    progressBarTop: number,
    company: CompanyDocument,
  ) {
    await forEachAsync(tiers, async (tier) => {
      const tierStamp =
        barConfig?.tierMilestones?.find(({ tierId }) =>
          areObjectIdsEqual(tier._id, tierId),
        ) ?? defaultTierStamp;
      if (!tierStamp) return;

      await this.drawTierMilestone(
        ctx,
        tierStamp,
        (this.BAR_ONLY_LAYOUT_WIDTH * tier[threshold]) / lastTier[threshold],
        progressBarTop,
        barConfig?.textColor,
        this.getBarDisplayText(barConfig, tier, company),
      );
    });
  }

  private getBarDisplayText(
    barConfig: BarConfig,
    tier: LoyaltyTierDocument,
    company: CompanyDocument,
  ): string[] {
    if (barConfig?.tierDisplayText?.length) return [];

    return barConfig.tierDisplayText.map((text) =>
      passBarDisplayTextGetters[text](
        tier,
        company.localization?.currency || 'QR',
      ),
    );
  }

  private async drawTierMilestone(
    ctx: SKRSContext2D,
    tierStamp: TierStamp,
    tierX: number,
    progressBarTop: number,
    textColor: string,
    passBarDisplayText: string[],
  ) {
    const barStart = (this.WIDTH - this.BAR_ONLY_LAYOUT_WIDTH) / 2;
    const iconCenterPointX =
      barStart - this.BAR_ONLY_LAYOUT_DISPLAY_SIZE + tierX;
    await this.drawBarDisplay(
      ctx,
      tierStamp.stamp,
      iconCenterPointX,
      progressBarTop,
    );
    this.drawBarDisplayText(
      ctx,
      tierStamp,
      textColor,
      passBarDisplayText,
      iconCenterPointX,
      progressBarTop,
    );
  }

  private async drawBarDisplay(
    ctx: SKRSContext2D,
    image: Image,
    baseX: number,
    baseY: number,
  ) {
    const baseSize = this.BAR_ONLY_LAYOUT_DISPLAY_SIZE;
    const scaledSize = baseSize * (image.scale ?? 1);
    const offset = (scaledSize - baseSize) / 2;
    const x = baseX - offset;
    const y = baseY - offset - (this.STROKE_WIDTH - 1);
    ctx.drawImage(await loadImage(image.url), x, y, scaledSize, scaledSize);
  }

  private drawBarDisplayText(
    ctx: SKRSContext2D,
    tierStamp: TierStamp,
    textColor: string,
    passBarDisplayText: string[],
    x: number,
    progressBarTop: number,
  ) {
    if (!passBarDisplayText || passBarDisplayText.length === 0) return;

    const baseSize = this.BAR_ONLY_LAYOUT_DISPLAY_SIZE;
    const scaledSize = baseSize * (tierStamp?.stamp?.scale ?? 1);
    const extraBottomSpace = Math.max(0, (scaledSize - baseSize) / 2);

    const fontSize = this.FONT_SIZE_S;
    const textPadding = 5;
    const lineHeight = fontSize + textPadding;

    for (let i = 0; i < passBarDisplayText.length; i++) {
      const y = progressBarTop + extraBottomSpace + lineHeight * i;

      this.drawBarDisplayTextLine(
        ctx,
        textColor,
        passBarDisplayText[i],
        fontSize,
        x,
        y,
      );
    }
  }

  private drawBarDisplayTextLine(
    ctx: SKRSContext2D,
    textColor: string,
    text: string,
    fontSize: number,
    x: number,
    y: number,
  ) {
    ctx.fillStyle = textColor ?? '#000000';
    ctx.font = `bold ${fontSize}px ${this.FONT_FAMILY}`;
    const textMetrics = ctx.measureText(text);
    ctx.fillText(
      text,
      x + this.BAR_ONLY_LAYOUT_DISPLAY_SIZE / 2 - textMetrics.width / 2,
      y + this.BAR_ONLY_LAYOUT_HEIGHT + fontSize,
    );
  }

  private async drawStampsForTiers(
    ctx: SKRSContext2D,
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
    stampsConfig: StampsConfig,
    stampsHeight: number,
  ) {
    this.loggerService.log('Drawing Stamps for Loyalty Tiers');
    const tiers = await this.getTiers(customer, loyaltyTierProgramProgress);

    const numIcons = Math.max(...tiers.map((tier) => tier.orderRateThreshold));

    await this.placeStamps(
      ctx,
      stampsHeight,
      numIcons,
      loyaltyTierProgramProgress.orderRate,
      await this.getIcons(stampsConfig),
      this.getTierStamps(tiers, stampsConfig?.tierStamps),
      this.generateStampRowContext(stampsConfig, numIcons),
    );
  }

  private generateStampRowContext(
    stampsConfig: StampsConfig,
    numIcons: number,
  ): StampRowContext {
    const numRows = this.getNumRoms(stampsConfig, numIcons);
    return {
      ...this.getStampRowContextFromConfig(stampsConfig),
      numRows,
      totalNumRows: numRows,
      skipRows: 0,
    };
  }

  private getStampRowContextFromConfig(stampsConfig: StampsConfig) {
    return {
      yOffset: stampsConfig?.yOffset ?? 0,
      xOffset: stampsConfig?.xOffset ?? 0,
      stampScale: stampsConfig?.stampScale ?? 1,
      areStampsStaggered: stampsConfig?.areStampsStaggered,
      iconPadding: stampsConfig?.iconPadding ?? this.ICON_PADDING,
    };
  }

  private async drawMinibar(
    ctx: SKRSContext2D,
    customer: CustomerDocument,
    minibarConfig: MinibarConfig,
    company: CompanyDocument,
    passBackgroundColor: string,
  ) {
    this.loggerService.log('Drawing Progress Bar');

    const lastMilestone = await this.customerTierInfoService.getLastMilestone(
      customer,
      company,
    );
    const nextMilestone = await this.customerTierInfoService.getNextMilestone(
      customer,
      company,
    );

    if (!nextMilestone) return;

    ctx.fillStyle = passBackgroundColor ?? '#FFFFFF';
    ctx.fillRect(
      0,
      this.HEIGHT - this.PROGRESS_BAR_AREA_HEIGHT,
      this.WIDTH,
      this.PROGRESS_BAR_AREA_HEIGHT,
    );

    const { start, end, progress } = this.getProgress(
      lastMilestone,
      nextMilestone,
    );

    const fillPercent = progress / end;
    this.drawProgressBars(
      ctx,
      minibarConfig?.outlineColor,
      minibarConfig?.fillColor,
      (this.WIDTH - this.PROGRESS_BAR_WIDTH) / 2,
      this.HEIGHT -
        this.PROGRESS_BAR_AREA_HEIGHT +
        this.PROGRESS_BAR_PADDING_TOP,
      this.PROGRESS_BAR_HEIGHT,
      this.PROGRESS_BAR_WIDTH,
      fillPercent,
    );

    const progressBarAreaTop =
      this.HEIGHT -
      this.PROGRESS_BAR_AREA_HEIGHT +
      this.PROGRESS_BAR_PADDING_TOP;
    const progressBarStart = (this.WIDTH - this.PROGRESS_BAR_WIDTH) / 2;
    const textHeight = progressBarAreaTop + this.PROGRESS_BAR_HEIGHT / 2 + 10;
    const textMargin = this.FONT_SIZE_M / 2;
    this.writeStartText(
      ctx,
      minibarConfig?.textColor,
      start.toString(),
      progressBarStart - textMargin,
      textHeight,
    );

    const progressBarFillEnd =
      progressBarStart + this.PROGRESS_BAR_WIDTH * fillPercent;
    this.writeProgressText(
      ctx,
      minibarConfig?.textColor,
      Math.round(progress).toString() + nextMilestone.currencySymbol,
      progressBarStart,
      progressBarFillEnd,
      textHeight,
    );

    const progressBarEnd = this.WIDTH / 2 + this.PROGRESS_BAR_WIDTH / 2;
    this.writeEndText(
      ctx,
      minibarConfig?.textColor,
      nextMilestone,
      progressBarEnd + textMargin,
      textHeight,
    );

    const progressBarBottom = progressBarAreaTop + this.PROGRESS_BAR_HEIGHT;
    this.writeMonthlySpendText(ctx, progressBarBottom);
  }

  private writeMonthlySpendText(ctx: SKRSContext2D, baseY: number) {
    ctx.font = `${this.FONT_SIZE_S}px ${this.FONT_FAMILY}`;
    const textMetrics = ctx.measureText(`Monthly Spend Meter`);
    ctx.fillText(
      `Monthly Spend Meter`,
      this.WIDTH / 2 - textMetrics.width / 2,
      baseY + this.FONT_SIZE_S,
    );
  }

  private writeEndText(
    ctx: SKRSContext2D,
    textColor: string,
    nextMilestone: LoyaltyTierMilestone,
    baseX: number,
    baseY: number,
  ) {
    if (!nextMilestone || nextMilestone.distanceToThreshold <= 0) return;
    ctx.fillStyle = textColor ?? '#000000';
    ctx.font = `bold ${this.FONT_SIZE_S}px ${this.FONT_FAMILY}`;
    ctx.fillText(
      `${Math.round(nextMilestone.distanceToThreshold)}${
        nextMilestone.currencySymbol
      } To`,
      baseX,
      baseY - 15,
    );
    ctx.fillText(`${nextMilestone.nameEn}`, baseX, baseY + 10);
  }

  private writeStartText(
    ctx: SKRSContext2D,
    textColor: string,
    startText: string,
    baseX: number,
    baseY: number,
  ) {
    ctx.font = `bold ${this.FONT_SIZE_M}px ${this.FONT_FAMILY}`;
    ctx.fillStyle = textColor ?? '#000000';
    const startTextMetrics = ctx.measureText(startText);
    ctx.fillText(startText, baseX - startTextMetrics.width, baseY);
  }

  private writeProgressText(
    ctx: SKRSContext2D,
    textColor: string,
    progressText: string,
    minX: number,
    baseX: number,
    y: number,
  ) {
    ctx.font = `bold ${this.FONT_SIZE_M}px ${this.FONT_FAMILY}`;
    ctx.fillStyle = textColor ?? '#000000';

    const startTextMetrics = ctx.measureText(' ' + progressText + '  ');
    const x = baseX - startTextMetrics.width;
    if (x < minX) return;

    ctx.fillText(progressText, x, y);
  }

  private drawProgressBars(
    ctx: SKRSContext2D,
    outlineColor: string,
    fillColor: string,
    x: number,
    y: number,
    height: number,
    width: number,
    fillPercent: number,
  ) {
    ctx.strokeStyle = fillColor ?? '#FFFFFF';
    ctx.lineWidth = this.STROKE_WIDTH;
    ctx.fillStyle = outlineColor ?? '#000000';
    this.drawBar(ctx, x, y, height, width);

    ctx.fillStyle = fillColor ?? '#FFFFFF';
    this.drawBar(ctx, x, y, height, width, fillPercent);
  }

  private drawBar(
    ctx: SKRSContext2D,
    x: number,
    y: number,
    height: number,
    baseWidth: number,
    fillPercent = 1,
  ) {
    const fillWidth = baseWidth * Math.min(fillPercent, 1) - this.STROKE_WIDTH;
    const width = Math.max(fillWidth, height);

    const radius = height / 2;
    ctx.beginPath();
    ctx.moveTo(x, y + radius);
    ctx.arcTo(x, y + height, x + radius, y + height, radius);
    ctx.arcTo(x + width, y + height, x + width, y + height - radius, radius);
    ctx.arcTo(x + width, y, x + width - radius, y, radius);
    ctx.arcTo(x, y, x, y + radius, radius);
    ctx.stroke();
    ctx.fill();
  }

  private getProgress(
    lastMilestone: LoyaltyTierMilestone,
    nextMilestone: LoyaltyTierMilestone,
  ): { start: number; end: number; progress: number } {
    if (!lastMilestone && nextMilestone)
      return {
        start: 0,
        end: nextMilestone.threshold,
        progress: nextMilestone.threshold - nextMilestone.distanceToThreshold,
      };

    if (lastMilestone && !nextMilestone)
      return {
        start: 0,
        end: lastMilestone.threshold,
        progress: lastMilestone.threshold,
      };

    return {
      start: lastMilestone.threshold,
      end: nextMilestone.threshold,
      progress: nextMilestone.threshold - nextMilestone.distanceToThreshold,
    };
  }

  private async drawStampsForPunchCard(
    ctx: SKRSContext2D,
    customer: CustomerDocument,
    stampsConfig: StampsConfig,
    stampsHeight: number,
  ) {
    if (!customer?.punchCardProgress || customer.punchCardProgress.length === 0)
      return null;
    this.loggerService.log('Drawing Stamps for Punch Card');

    const progresses = await this.getProgressesToDraw(customer);
    if (!progresses || progresses.length === 0) return null;

    const hasMultipleTracks = progresses.length > 1;
    await forEachAsync(progresses, async (progress, i) => {
      const punchCard = progress.punchCard;
      if (!punchCard?.achievements || punchCard.achievements.length === 0)
        return null;

      const numIcons = Math.min(
        this.punchCardService.getCompletionThreshold(punchCard),
        this.MAX_ICONS,
      );

      await this.placeStamps(
        ctx,
        stampsHeight,
        numIcons,
        progress.count,
        await this.getIcons(stampsConfig, punchCard?._id),
        this.getPunchCardStamps(stampsConfig, punchCard, numIcons),
        {
          ...this.getStampRowContextFromConfig(stampsConfig),
          numRows: hasMultipleTracks
            ? 1
            : this.getNumRoms(stampsConfig, numIcons),
          totalNumRows: hasMultipleTracks
            ? progresses.length
            : this.getNumRoms(stampsConfig, numIcons),
          skipRows: i,
        },
      );
    });
  }

  private async getProgressesToDraw(
    customer: CustomerDocument,
  ): Promise<PunchCardProgress[]> {
    const uniquePunchCardIds = new Set(
      customer.punchCardProgress
        .filter((progress) => !progress.completedAt)
        .filter((progress) => progress.punchCard.achievements.length > 0)
        .map((progress) => progress.punchCard._id.toString()),
    );
    return await mapAsync(Array.from(uniquePunchCardIds), (punchCardId) =>
      this.getProgressToDraw(customer, new Types.ObjectId(punchCardId)),
    );
  }

  private async getProgressToDraw(
    customer: CustomerDocument,
    punchCardId: Types.ObjectId,
  ): Promise<PunchCardProgress> {
    const progresses = customer.punchCardProgress.filter((progress) =>
      punchCardId.equals(progress.punchCard._id),
    );
    const currentProgress = progresses.find(
      (progress) => !progress.completedAt,
    );
    if (!currentProgress) return null;
    if (currentProgress.count > 0) return currentProgress;

    const completedProgress =
      await this.completedPunchCardService.findLatestProgress(
        customer._id,
        punchCardId,
      );
    return completedProgress || currentProgress;
  }

  private getNumRoms(stampsConfig: StampsConfig, numIcons: number): number {
    if (stampsConfig?.numRows) return stampsConfig.numRows;
    return numIcons < 6 ? 1 : numIcons < 15 ? 2 : 3;
  }

  private async placeStamps(
    ctx: SKRSContext2D,
    height: number,
    numIcons: number,
    numFilledStamps: number,
    [emptyIcon, filledIcon]: [LoadedImage, LoadedImage],
    specialStampMap: Record<number, SpecialStamp>,
    {
      numRows,
      totalNumRows,
      skipRows,
      yOffset,
      xOffset,
      stampScale,
      areStampsStaggered,
      iconPadding,
    }: StampRowContext,
  ) {
    const iconsPerRow = this.getIconsPerRow(numIcons, numRows);
    const iconSize = this.getIconSize(
      numRows,
      iconsPerRow[0],
      height,
      stampScale,
      iconPadding,
    );

    const iconSpace = iconSize + iconPadding;
    const verticalMargin = this.getVerticalMargin(
      totalNumRows,
      iconSize,
      iconSpace,
      height,
    );

    const drawSpecialStamp = this.getDrawSpecialStamp(ctx, iconSize);
    for (let row = 0; row < numRows; row++) {
      const idealHorizontalMargin =
        (this.WIDTH - iconsPerRow[row] * iconSpace) / 2;
      const horizontalMargin =
        areStampsStaggered || idealHorizontalMargin > this.MIN_HORIZONTAL_MARGIN
          ? idealHorizontalMargin
          : this.MIN_HORIZONTAL_MARGIN;
      const baseY = verticalMargin + (row + skipRows) * iconSpace + yOffset;
      const stampsPlaced = iconsPerRow.slice(0, row).reduce((a, b) => a + b, 0);
      for (let i = 0; i < iconsPerRow[row]; i++) {
        const x = horizontalMargin + xOffset + i * iconSpace;

        const stampNumber = stampsPlaced + i;
        const isFilled = stampNumber < numFilledStamps;
        const icon = isFilled ? filledIcon : emptyIcon;
        const iconScaledSize = iconSize * (icon.scale ?? 1);
        const specialStamp = specialStampMap[stampNumber];

        const y = areStampsStaggered
          ? baseY + (icon.image.height / 4) * (i % 2 ? 1 : -1)
          : baseY;

        if (specialStamp) await drawSpecialStamp(specialStamp, x, y, isFilled);
        else ctx.drawImage(icon.image, x, y, iconScaledSize, iconScaledSize);
      }
    }
  }

  private getIconSize(
    numRows: number,
    maxIconsPerRow: number,
    height: number,
    stampScale: number,
    iconPadding: number,
  ) {
    const paddingWidth = (maxIconsPerRow - 1) * iconPadding;
    const marginWidth = 2 * this.MIN_HORIZONTAL_MARGIN;
    const totalWidthForIcons = this.WIDTH - paddingWidth - marginWidth;
    const maxHorizontalIconSize = totalWidthForIcons / maxIconsPerRow;

    const paddingHeight = (numRows - 1) * iconPadding;
    const marginHeight = 2 * this.MIN_VERTICAL_MARGIN;
    const totalHeightForIcons = height - paddingHeight - marginHeight;
    const maxVerticalIconSize = totalHeightForIcons / numRows;

    const iconSize = Math.min(
      maxHorizontalIconSize,
      maxVerticalIconSize,
      this.MAX_ICON_SIZE,
    );
    const scaledIconSize = iconSize * (stampScale ?? 1);
    return scaledIconSize;
  }

  private getVerticalMargin(
    numRows: number,
    iconSize: number,
    iconSpace: number,
    height: number,
  ) {
    const isSingleRow = numRows === 1;

    const centerRowY = height / 2 - iconSize / 2;
    const freeVerticalSpace = (height - numRows * iconSpace) / 2;

    return isSingleRow
      ? centerRowY
      : Math.max(freeVerticalSpace, this.MIN_VERTICAL_MARGIN);
  }

  private async getTiers(
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ) {
    const loyaltyTiers = await this.loyaltyTierIndexService.index({
      companyId: customer.company,
      type: LoyaltyTierSearchType.NORMAL,
    });

    const currentTier = loyaltyTiers.find((tier) =>
      tier._id.equals(customer.loyaltyTier?._id),
    );
    const currentTierIndex = currentTier ? currentTier.tierIndex : 0;

    return loyaltyTiers
      .filter((tier) =>
        this.isTierDrawable(tier, currentTierIndex, loyaltyTierProgramProgress),
      )
      .sort(
        (tierA, tierB) =>
          tierA.orderRateThreshold - tierB.orderRateThreshold ||
          tierA.amountSpentThreshold - tierB.amountSpentThreshold ||
          tierA.pointsRateThreshold - tierB.pointsRateThreshold,
      );
  }

  private isTierDrawable(
    tier: LoyaltyTierDocument,
    currentTierIndex: number,
    { orderRate, amountSpent, pointsRate }: LoyaltyTierProgramProgress,
  ): boolean {
    const willTierOverflow =
      tier.orderRateThreshold && tier.orderRateThreshold > this.MAX_ICONS;
    if (willTierOverflow) return false;

    const isCurrentOrEarnable = tier.tierIndex >= currentTierIndex;
    if (isCurrentOrEarnable) return true;

    const isOrderRateThresholdMet =
      tier.orderRateThreshold && tier.orderRateThreshold < orderRate;

    const isAmountSpentThresholdMet =
      tier.amountSpentThreshold && tier.amountSpentThreshold < amountSpent;

    const isPointsRateThresholdMet =
      tier.pointsRateThreshold && tier.pointsRateThreshold < pointsRate;

    const isPastTier =
      isOrderRateThresholdMet ||
      isAmountSpentThresholdMet ||
      isPointsRateThresholdMet;
    if (isPastTier) return true;

    return false;
  }

  private getIconsPerRow(numIcons: number, numRows: number): number[] {
    const minIconsInRow = Math.floor(numIcons / numRows);
    const rowsWithExtraIcon = numIcons % numRows;

    const minIconsPerRow = new Array(numRows).fill(minIconsInRow);
    return minIconsPerRow.map((numIcons: number, i: number): number =>
      i < rowsWithExtraIcon ? numIcons + 1 : numIcons,
    );
  }

  private async drawBackground(
    ctx: SKRSContext2D,
    stripImageConfig: StripImageConfig,
  ) {
    const backgroundImage = stripImageConfig?.backgroundImage;
    if (backgroundImage) {
      const loadedImage = await loadImage(backgroundImage.url);
      const width = this.WIDTH * (stripImageConfig?.backgroundImageWidth ?? 1);
      const startX = this.computeStartX(stripImageConfig);
      return ctx.drawImage(loadedImage, startX, 0, width, this.HEIGHT);
    }

    const offwhite = '#F3F6F9';
    ctx.fillStyle = stripImageConfig?.backgroundColor ?? offwhite;
    ctx.fillRect(0, 0, this.WIDTH, this.HEIGHT);
  }

  private getTierStamps(
    tiers: LoyaltyTierDocument[],
    tierStamps: StatefulTierStamp[],
  ): Record<number, StatefulTierStamp> {
    if (!tiers?.length || !tierStamps?.length) return [];

    const numStamps = Math.max(...tiers.map((tier) => tier.orderRateThreshold));
    if (!Number.isSafeInteger(numStamps)) return [];

    const defaultTierStamp = tierStamps.find((tierStamp) => !tierStamp.tierId);

    return new Array(numStamps).fill(null).map((_, i) => {
      const tierAtIndex = tiers.find(
        (tier) => tier.orderRateThreshold - 1 === i,
      );

      if (!tierAtIndex) return null;

      const tierStamp = tierStamps.find((tierStamp) =>
        areObjectIdsEqual(tierAtIndex._id, tierStamp.tierId),
      );
      return tierStamp ?? defaultTierStamp;
    });
  }

  private getPunchCardStamps(
    stampsConfig: StampsConfig,
    punchCard: EmbeddedPunchCardDto,
    numIcons: number,
  ): Record<number, PunchCardStamp> {
    return new Array(numIcons)
      .fill(null)
      .map((_, i) => this.getAchievementStampAt(stampsConfig, punchCard, i));
  }

  private getAchievementStampAt(
    stampsConfig: StampsConfig,
    punchCard: EmbeddedPunchCardDto,
    index: number,
  ): PunchCardStamp | null {
    if (!stampsConfig?.punchCardStamps?.length) return null;

    const previousAchievement =
      punchCard.hasStampOnRedeem &&
      punchCard.achievements.find(
        (achievement) => achievement?.requirement?.targetValue === index,
      );
    if (previousAchievement)
      return (
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            areObjectIdsEqual(stamp.achievementId, previousAchievement._id) &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_REDEEM,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            areObjectIdsEqual(stamp.achievementId, previousAchievement._id) &&
            stamp.type === PunchCardStampType.ACHIEVEMENT,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            areObjectIdsEqual(stamp.achievementId, previousAchievement._id) &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_EARN,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            !stamp.achievementId &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_REDEEM,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            !stamp.achievementId &&
            stamp.type === PunchCardStampType.ACHIEVEMENT,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            !stamp.achievementId &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_EARN,
        )
      );

    const currentAchievement =
      punchCard.hasStampOnEarn &&
      punchCard.achievements.find(
        (achievement) => achievement?.requirement?.targetValue === index + 1,
      );
    if (currentAchievement)
      return (
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            areObjectIdsEqual(stamp.achievementId, previousAchievement._id) &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_EARN,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            areObjectIdsEqual(stamp.achievementId, previousAchievement._id) &&
            stamp.type === PunchCardStampType.ACHIEVEMENT,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            areObjectIdsEqual(stamp.achievementId, previousAchievement._id) &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_REDEEM,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            !stamp.achievementId &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_EARN,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            !stamp.achievementId &&
            stamp.type === PunchCardStampType.ACHIEVEMENT,
        ) ||
        stampsConfig.punchCardStamps.find(
          (stamp) =>
            !stamp.achievementId &&
            stamp.type === PunchCardStampType.ACHIEVEMENT_ON_REDEEM,
        )
      );

    return null;
  }

  private getDrawImage(ctx: SKRSContext2D, iconSize: number) {
    const imageSize = iconSize + this.BORDER_WIDTH;
    const borderWidthOffset = this.BORDER_WIDTH / 2;
    return async (image: Image, x: number, y: number) => {
      const canvasImage = await loadImage(image.url);
      ctx.drawImage(
        canvasImage,
        x - borderWidthOffset,
        y - borderWidthOffset,
        imageSize * (image.scale ?? 1),
        imageSize * (image.scale ?? 1),
      );
    };
  }

  private getDrawSpecialStamp(ctx: SKRSContext2D, iconSize: number) {
    const drawImage = this.getDrawImage(ctx, iconSize);
    return async (
      specialStamp: SpecialStamp,
      x: number,
      y: number,
      isFilled: boolean,
    ) => {
      if ('stamp' in specialStamp) return drawImage(specialStamp.stamp, x, y);
      else if (isFilled) return drawImage(specialStamp.emptyStamp, x, y);
      else return drawImage(specialStamp.filledStamp, x, y);
    };
  }

  private async getIcons(
    stampsConfig: StampsConfig,
    punchCardId?: Types.ObjectId,
  ): Promise<[LoadedImage, LoadedImage]> {
    return await Promise.all([
      this.getIcon(StripIcon.EMPTY, stampsConfig, punchCardId),
      this.getIcon(StripIcon.FILLED, stampsConfig, punchCardId),
    ]);
  }

  private async getIcon(
    icon: StripIcon,
    stampsConfig: StampsConfig,
    punchCardId?: Types.ObjectId,
  ): Promise<LoadedImage> {
    try {
      const defaultIconPath =
        icon === StripIcon.EMPTY
          ? IMAGE_URLS.EMPTY_STAMP
          : IMAGE_URLS.FILLED_STAMP;

      const configIcon =
        icon === StripIcon.EMPTY
          ? stampsConfig?.emptyStamp
          : stampsConfig?.filledStamp;

      const punchCardStamp =
        punchCardId && stampsConfig?.punchCardStamps?.length
          ? stampsConfig.punchCardStamps.find(
              (stamp) =>
                areObjectIdsEqual(stamp.punchCardId, punchCardId) &&
                stamp.type === PunchCardStampType.PUNCH_CARD,
            ) ||
            stampsConfig.punchCardStamps.find(
              (stamp) =>
                !stamp.punchCardId &&
                stamp.type === PunchCardStampType.PUNCH_CARD,
            )
          : undefined;

      const punchCardIcon =
        icon === StripIcon.EMPTY
          ? punchCardStamp?.emptyStamp
          : punchCardStamp?.filledStamp;

      const configuredIcon = punchCardIcon ?? configIcon;
      const iconPath = configuredIcon?.url ?? defaultIconPath;

      this.loggerService.log(`Loading icon from path: ${iconPath}`, {
        icon,
        configuredIcon,
        defaultIconPath,
      });

      if (!iconPath) {
        throw new Error('Icon path is undefined or empty');
      }

      try {
        new URL(iconPath);
      } catch (e) {
        throw new Error(`Invalid URL format for icon: ${iconPath}`);
      }

      const image = await loadImage(iconPath).catch((error) => {
        throw new Error(
          `Failed to load image from ${iconPath}: ${error.message}`,
        );
      });

      return { image, scale: configuredIcon?.scale ?? 1 };
    } catch (error) {
      this.loggerService.error(`Error loading ${icon} icon: ${error.message}`);

      const defaultPath =
        icon === StripIcon.EMPTY
          ? IMAGE_URLS.EMPTY_STAMP
          : IMAGE_URLS.FILLED_STAMP;

      const defaultImage = await loadImage(defaultPath);
      return { image: defaultImage, scale: 1 };
    }
  }

  private computeStartX(stripImageConfig: StripImageConfig): number {
    return (
      (this.WIDTH * (1 - (stripImageConfig?.backgroundImageWidth ?? 1))) / 2
    );
  }
}
