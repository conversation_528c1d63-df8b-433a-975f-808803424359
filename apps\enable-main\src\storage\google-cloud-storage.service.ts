import {
  CollectionName,
  DocumentRedirectDocument,
  Image,
  ImageToCreate,
  LoggerService,
  omit,
  UploadPassConfigImageResponseDto,
} from '@app/shared-stuff';
import { File, Storage } from '@google-cloud/storage';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model } from 'mongoose';

@Injectable()
export class GoogleCloudStorageService implements OnModuleInit {
  private logger = new LoggerService(GoogleCloudStorageService.name);
  private storage: Storage;
  private bucketName: string;
  constructor(
    private readonly configService: ConfigService,
    @InjectModel(CollectionName.DOCUMENT_REDIRECT)
    private readonly documentRedirectModel: Model<DocumentRedirectDocument>,
  ) {}

  public onModuleInit() {
    this.storage = new Storage({
      projectId: this.configService.get('GOOGLE_CLOUD_STORAGE_PROJECT_ID'),
      credentials: JSON.parse(
        this.configService.get('GOOGLE_CLOUD_STORAGE_CREDENTIALS'),
      ),
    });
    this.bucketName = this.configService.get('GOOGLE_CLOUD_STORAGE_BUCKET');
  }

  public async getSignedUrl(
    filePath: string,
  ): Promise<UploadPassConfigImageResponseDto> {
    const file = this.getFile(filePath);
    const [uploadUrl] = await file.getSignedUrl({
      action: 'write',
      version: 'v4',
      expires: moment.utc().add(1, 'day').toDate(),
    });

    return { publicUrl: file.publicUrl(), uploadUrl };
  }

  public async upload(buffer: Buffer, filePath: string): Promise<string> {
    const file = this.getFile(filePath);
    await file.save(buffer);
    return file.publicUrl().replaceAll('%2F', '/');
  }

  public async uploadDocument(buffer: Buffer, name: string): Promise<string> {
    const url = await this.upload(buffer, `documents/${name}`);
    await this.documentRedirectModel.create({ name, url });
    return url;
  }

  public async uploadImage(image: ImageToCreate): Promise<Image> {
    try {
      const publicUrl = await this.upload(
        Buffer.from(image.base64, 'base64'),
        this.getImageFilePath(image),
      );

      return {
        ...omit(image, ['base64']),
        url: publicUrl,
      };
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }

  private getFile(filePath: string): File {
    return this.storage.bucket(this.bucketName).file(filePath);
  }

  private getImageFilePath(image: ImageToCreate | Image): string {
    return `images/${image.for}/${image.name}`;
  }

  async getSignedUploadUrl(
    filePath: string,
    contentType: string,
  ): Promise<string> {
    const [url] = await this.storage
      .bucket(this.bucketName)
      .file(filePath)
      .getSignedUrl({
        action: 'write',
        version: 'v4',
        expires: Date.now() + 10 * 60 * 1000,
        contentType,
      });
    return url;
  }
}
